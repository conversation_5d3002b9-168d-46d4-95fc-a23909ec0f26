import 'package:connectify_app/src/screens/announcement/model/announcement_model.dart';
import 'package:connectify_app/src/shared/data/remote/api_endpoints.dart';
import 'package:connectify_app/src/shared/providers/network_api_service_provider.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:xr_helper/xr_helper.dart';

final announcementRepoProvider = Provider((ref) {
  final networkApiServices = ref.watch(networkServiceProvider);

  return AnnouncementRepo(networkApiServices);
});

class AnnouncementRepo with BaseRepository {
  final BaseApiServices _networkApiServices;

  AnnouncementRepo(this._networkApiServices);

  //? Get Announcements Data ========================================================
  Future<List<AnnouncementModel>> getAnnouncements() async {
    return await baseFunction(() async {
      final response = await _networkApiServices.getResponse(
        ApiEndpoints.announcements,
      );

      final announcementData =
          await compute(responseToAnnouncementModelList, response);

      return announcementData;
    });
  }

  //? Get Announcements Data with Pagination ========================================================
  Future<List<AnnouncementModel>> getAnnouncementsPaginated(
      {int page = 1}) async {
    return await baseFunction(() async {
      final response = await _networkApiServices.getResponse(
        '${ApiEndpoints.announcements}&${ApiEndpoints.pagination(page)}',
      );

      final announcementData =
          await compute(responseToAnnouncementModelList, response);

      return announcementData;
    });
  }


}
