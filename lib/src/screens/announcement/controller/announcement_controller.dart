import 'package:connectify_app/src/screens/announcement/model/announcement_model.dart';
import 'package:connectify_app/src/screens/announcement/repo/announcement_repo.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:xr_helper/xr_helper.dart';

final announcementControllerProvider =
    Provider.family<AnnouncementController, BuildContext>((ref, context) {
  final announcementRepo = ref.watch(announcementRepoProvider);

  return AnnouncementController(
      announcementRepo: announcementRepo, context: context);
});

final announcementChangeNotifierControllerProvider =
    ChangeNotifierProvider.family<AnnouncementController, BuildContext>(
        (ref, context) {
  final announcementRepo = ref.watch(announcementRepoProvider);

  return AnnouncementController(
      announcementRepo: announcementRepo, context: context);
});

final getAnnouncementsDataProvider =
    FutureProvider.family<List<AnnouncementModel>, BuildContext>(
        (ref, context) async {
  final announcementCtrl = ref.watch(announcementControllerProvider(context));

  return await announcementCtrl.getAnnouncements();
});

final getAnnouncementsDataProviderWithPagination =
    FutureProvider.family<List<AnnouncementModel>, (BuildContext, int)>(
  (ref, params) async {
    final context = params.$1;
    final page = params.$2;
    final announcementCtrl = ref.watch(announcementControllerProvider(context));

    return await announcementCtrl.getAnnouncementsPaginated(page: page);
  },
);

class AnnouncementController extends BaseVM {
  final AnnouncementRepo announcementRepo;
  final BuildContext context;

  AnnouncementController({
    required this.announcementRepo,
    required this.context,
  });

  Future<List<AnnouncementModel>> getAnnouncements() async {
    return await baseFunction(context, () async {
      final announcementData = await announcementRepo.getAnnouncements();

      return announcementData;
    });
  }

  Future<List<AnnouncementModel>> getAnnouncementsPaginated(
      {int page = 1}) async {
    return await baseFunction(context, () async {
      final announcementData =
          await announcementRepo.getAnnouncementsPaginated(page: page);

      return announcementData;
    });
  }
}
