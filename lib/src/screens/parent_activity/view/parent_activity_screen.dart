import 'package:connectify_app/src/screens/home/<USER>/main_screen.dart';
import 'package:connectify_app/src/screens/parent_activity/view/widgets/parnet_activity_list.dart';
import 'package:connectify_app/src/shared/extensions/context_extensions.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:xr_helper/xr_helper.dart';

import '../../home/<USER>/main_screen/widgets/main_app_bar.dart';

class ParentActivitiesScreen extends ConsumerWidget {
  const ParentActivitiesScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return WillPopScope(
      onWillPop: () async {
        context.toReplacement(const MainScreen());
        return false;
      },
      child: <PERSON><PERSON><PERSON>(
        child: Scaffold(
          appBar: MainAppBar(
            title: context.tr.activities,
            isBackButton: true,
          ),
          body: const ParentActivityList()
              .paddingAll(AppSpaces.mediumPadding)
              .scroll(),
        ),
      ),
    );
  }
}
