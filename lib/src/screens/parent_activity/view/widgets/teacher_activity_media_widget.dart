import 'dart:io';

import 'package:connectify_app/src/shared/consts/app_constants.dart';
import 'package:connectify_app/src/shared/extensions/context_extensions.dart';
import 'package:connectify_app/src/shared/shared_models/base_media_model.dart';
import 'package:connectify_app/src/shared/widgets/shared_widgets.dart';
import 'package:dio/dio.dart';
import 'package:flutter/material.dart';
import 'package:flutter_media_downloader/flutter_media_downloader.dart';
import 'package:gal/gal.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:xr_helper/xr_helper.dart';

final _flutterMediaDownloaderPlugin = MediaDownload();

class TeacherActivityMediaWidget extends HookConsumerWidget {
  final List<BaseMediaModel> mediaList;

  const TeacherActivityMediaWidget({
    super.key,
    required this.mediaList,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return ListView.separated(
      padding: const EdgeInsets.all(AppSpaces.mediumPadding),
      itemCount: mediaList.length,
      separatorBuilder: (context, index) => context.mediumGap,
      itemBuilder: (context, index) {
        final mediaItem = mediaList[index];

        return ClipRRect(
          borderRadius: BorderRadius.circular(AppRadius.baseContainerRadius),
          child: SizedBox(
            height: 150,
            child: Stack(
              children: [
                Image.network(
                  mediaItem.url ?? '',
                  fit: BoxFit.cover,
                  width: double.infinity,
                  errorBuilder: (context, error, stackTrace) =>
                      const BaseCachedImage(
                    AppConsts.activityPlaceholder,
                  ),
                ),
                Positioned(
                  right: 0,
                  bottom: 0,
                  child: IconButton(
                    icon: const CircleAvatar(
                      backgroundColor: ColorManager.blueColor,
                      child: Icon(
                        Icons.download,
                        color: Colors.white,
                      ),
                    ),
                    onPressed: () async {
                      final imagePath =
                          '${Directory.systemTemp.path}/${mediaItem.publicId}.jpg';
                      await Dio().download(mediaItem.url ?? '', imagePath);
                      await Gal.putImage(imagePath);

                      context.showBarMessage(context.tr.downloadedSuccessfully);

                      // _flutterMediaDownloaderPlugin.downloadMedia(
                      //   context,
                      //   mediaItem.url ?? '',
                      // );
                    },
                  ),
                ),
                // view eye icon for full screen image
                Positioned(
                  right: 0,
                  top: 0,
                  child: IconButton(
                    icon: const CircleAvatar(
                      backgroundColor: ColorManager.primaryColor,
                      child: Icon(
                        Icons.remove_red_eye,
                        color: Colors.white,
                      ),
                    ),
                    onPressed: () {
                      showDialog(
                        context: context,
                        builder: (context) => Dialog(
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(
                                AppRadius.baseContainerRadius),
                          ),
                          child: ClipRRect(
                            borderRadius: BorderRadius.circular(
                                AppRadius.baseContainerRadius),
                            child: Image.network(
                              mediaItem.url ?? '',
                              errorBuilder: (context, error, stackTrace) =>
                                  const BaseCachedImage(
                                AppConsts.activityPlaceholder,
                              ),
                            ),
                          ),
                        ),
                      );
                    },
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }
}
