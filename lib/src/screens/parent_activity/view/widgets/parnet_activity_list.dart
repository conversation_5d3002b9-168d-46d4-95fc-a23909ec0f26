import 'package:collection/collection.dart';
import 'package:connectify_app/src/screens/class/view/classes_details_screen/widgets/date_attendance_filter_widget.dart';
import 'package:connectify_app/src/screens/food/model/food_model.dart';
import 'package:connectify_app/src/screens/parent_activity/view/widgets/parent_activity_card.dart';
import 'package:connectify_app/src/screens/sleep/controller/sleep_controller.dart';
import 'package:connectify_app/src/screens/sleep/model/sleep_model.dart';
import 'package:connectify_app/src/screens/toilet/model/toilet_model.dart';
import 'package:connectify_app/src/shared/extensions/context_extensions.dart';
import 'package:connectify_app/src/shared/extensions/riverpod_extensions.dart';
import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:intl/intl.dart';
import 'package:table_calendar/table_calendar.dart';
import 'package:xr_helper/xr_helper.dart';

import '../../../food/controller/food_controller.dart';
import '../../../toilet/controller/toilet_controller.dart';
import '../../controllers/teacher_activities_controller.dart';
import '../../models/teacher_activity_model.dart';

class ParentActivityList extends HookConsumerWidget {
  const ParentActivityList({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final allDatesOfYear = generateAllDatesOfYear();
    final today = DateTime.now();
    final todayString = DateFormat('yyyy-MM-dd', 'en').format(today);

    final initialIndex = allDatesOfYear.indexWhere(
      (date) => date['date'] == todayString,
    );

    final selectedIndex = useState(initialIndex >= 0 ? initialIndex : 0);
    final activityParams = (
      context,
      allDatesOfYear[selectedIndex.value]['date'].toString(),
      allDatesOfYear[selectedIndex.value]['day'].toString()
    );

    final params = (
      context,
      DateTime.parse(allDatesOfYear[selectedIndex.value]['date'].toString()),
    );

    final getTeacherActivityCtrl =
        ref.watch(getTeacherActivitiesByDateProvider(activityParams));

    final foodCtrl = ref.watch(getFoodByDayData(params));
    final mealCtrl = ref.watch(getMealsProvider(context));
    final toiletCtrl = ref.watch(getToiletByDayData(params));
    final sleepCtrl = ref.watch(getSleepByDayData(params));

    final toiletList = toiletCtrl.when(
      data: (data) => data,
      loading: () => <ToiletModel>[],
      error: (error, stack) => <ToiletModel>[],
    );

    final foodList = foodCtrl.when(
      data: (data) => data,
      loading: () => <FoodModel>[],
      error: (error, stack) => <FoodModel>[],
    );

    final sleepList = sleepCtrl.when(
      data: (data) => data,
      loading: () => <SleepModel>[],
      error: (error, stack) => <SleepModel>[],
    );

    final mealsList = mealCtrl.when(
      data: (data) => data,
      loading: () => <MealModel>[],
      error: (error, stack) => <MealModel>[],
    );

    return Column(
      children: [
        DateAttendanceFilterWidget(
          selectedDay: allDatesOfYear[selectedIndex.value]['day']!,
          date: allDatesOfYear[selectedIndex.value]['date']!,
          onNext: () {
            if (selectedIndex.value < allDatesOfYear.length - 1) {
              selectedIndex.value++;
            }
          },
          onPrevious: () {
            if (selectedIndex.value > 0) {
              selectedIndex.value--;
            }
          },
        ),
        context.mediumGap,
        getTeacherActivityCtrl.get(
          data: (teacherActivity) {
            return HookBuilder(builder: (context) {
              final combinedActivities = useState<List<dynamic>>([]);
              final teacherActivitiesForDay =
                  useState<List<TeacherActivityModel>>([]);
              final toiletListForDay = useState<List<ToiletModel>>([]);
              final foodListForDay = useState<List<FoodModel>>([]);
              final sleepListForDay = useState<List<SleepModel>>([]);
              final meals = useState<List<MealModel>>([]);

              useEffect(() {
                final selectedDate = getDateFromSelectedIndex(
                    selectedIndex.value, allDatesOfYear);

                teacherActivitiesForDay.value = teacherActivity
                    .where((element) =>
                        (element.isWeekly &&
                            element.day ==
                                allDatesOfYear[selectedIndex.value]['day']) ||
                        (!element.isWeekly &&
                            element.date ==
                                allDatesOfYear[selectedIndex.value]['date']))
                    .toList();

                // Filter toilet, food, and sleep activities based on selected date
                toiletListForDay.value = toiletList
                    .where(
                        (element) => isSameDay(element.createdAt, selectedDate))
                    .toList();

                foodListForDay.value = foodList
                    .where(
                        (element) => isSameDay(element.createdAt, selectedDate))
                    .toList();

                meals.value = mealsList;

                sleepListForDay.value = sleepList
                    .where(
                        (element) => isSameDay(element.createdAt, selectedDate))
                    .toList();

                combinedActivities.value = <dynamic>[
                  ...teacherActivitiesForDay.value,
                  ...toiletListForDay.value,
                  ...foodListForDay.value.map(
                    (e) {
                      return FoodModel(
                        id: e.id,
                        mealType: e.mealType,
                        mealAmount: e.mealAmount,
                        createdAt: e.createdAt,
                        meal: meals.value
                                .firstWhereOrNull((meal) =>
                                    meal.mealType == e.mealType &&
                                    meal.day?.name ==
                                        selectedDate.formatToDayName)
                                ?.name ??
                            '-',
                      );
                    },
                  ),
                  ...sleepListForDay.value,
                ];

                combinedActivities.value.sort((a, b) {
                  DateTime getActivityTime(dynamic activity) {
                    if (activity is TeacherActivityModel) {
                      final startTime =
                          DateFormat('HH:mm', 'en').parse(activity.startTime);
                      return DateTime(
                        selectedDate.year,
                        selectedDate.month,
                        selectedDate.day,
                        startTime.hour,
                        startTime.minute,
                      );
                    } else if (activity is ToiletModel ||
                        activity is FoodModel ||
                        activity is SleepModel) {
                      return (activity.createdAt as DateTime?) ??
                          DateTime.now();
                    }
                    return DateTime.now();
                  }

                  return getActivityTime(a).compareTo(getActivityTime(b));
                });

                return () {};
              }, [
                selectedIndex.value,
                teacherActivity,
                toiletList,
                foodList,
                sleepList
              ]);

              return Column(
                children: [
                  if (combinedActivities.value.isEmpty) ...[
                    Column(
                      children: [
                        context.xxLargeGap,
                        Text(
                          context.tr.noActivities,
                          style: context.headLine,
                        ),
                      ],
                    ),
                  ] else ...[
                    ListView.builder(
                      physics: const NeverScrollableScrollPhysics(),
                      shrinkWrap: true,
                      itemCount: combinedActivities.value.length,
                      itemBuilder: (context, index) {
                        final activity = combinedActivities.value[index];
                        return ParentActivityCard(
                          activityModel: activity,
                          selectedDate: allDatesOfYear[selectedIndex.value]
                              ['date']!,
                        );
                      },
                    ),
                  ],
                ],
              );
            });
          },
        ),
      ],
    );
  }

  DateTime getDateFromSelectedIndex(
      int index, List<Map<String, String>> allDatesOfYear) {
    final selectedDateString = allDatesOfYear[index]['date']!;
    return DateFormat('yyyy-MM-dd', 'en').parse(selectedDateString);
  }

  List<Map<String, String>> generateAllDatesOfYear() {
    final now = DateTime.now();
    final startOfYear = DateTime(now.year, 1, 1);
    final endOfYear = DateTime(now.year, 12, 31);
    final dates = <Map<String, String>>[];

    for (var date = startOfYear;
        date.isBefore(endOfYear.add(const Duration(days: 1)));
        date = date.add(const Duration(days: 1))) {
      dates.add({
        'day': DateFormat('EEEE', 'en').format(date),
        'date': DateFormat('yyyy-MM-dd', 'en').format(date),
      });
    }
    return dates;
  }
}

// class ParentActivityList extends HookConsumerWidget {
//   const ParentActivityList({super.key});
//
//   @override
//   Widget build(BuildContext context, WidgetRef ref) {
//     final allDatesOfYear = generateAllDatesOfYear();
//     final today = DateTime.now();
//     final todayString = DateFormat('yyyy-MM-dd').format(today);
//
//     final initialIndex = allDatesOfYear.indexWhere(
//           (date) => date['date'] == todayString,
//     );
//     final selectedIndex = useState(initialIndex >= 0 ? initialIndex : 0);
//
//     final getTeacherActivityCtrl =
//         ref.watch(getTeacherActivitiesDataProvider(context));
//
//     final toiletCtrl = ref.watch(getAllToiletData(context));
//     final foodCtrl = ref.watch(getAllFoodData(context));
//     final sleepCtrl = ref.watch(getAllSleepData(context));
//
//     final toiletList = toiletCtrl.when(
//       data: (data) => data,
//       loading: () => <ToiletModel>[],
//       error: (error, stack) => <ToiletModel>[],
//     );
//
//     final foodList = foodCtrl.when(
//       data: (data) => data,
//       loading: () => <FoodModel>[],
//       error: (error, stack) => <FoodModel>[],
//     );
//
//     final sleepList = sleepCtrl.when(
//       data: (data) => data,
//       loading: () => <SleepModel>[],
//       error: (error, stack) => <SleepModel>[],
//     );
//
//     return getTeacherActivityCtrl.get(
//       data: (teacherActivity) {
//         return HookBuilder(builder: (context) {
//
//
//           final combinedActivities = useState<List<dynamic>>([]);
//           final teacherActivitiesForDay =
//               useState<List<TeacherActivityModel>>([]);
//           final toiletListForDay = useState<List<ToiletModel>>([]);
//           final foodListForDay = useState<List<FoodModel>>([]);
//           final sleepListForDay = useState<List<SleepModel>>([]);
//
//           useEffect(() {
//             final selectedDate =
//                 getDateFromSelectedIndex(selectedIndex.value, allDatesOfYear);
//
//             teacherActivitiesForDay.value = teacherActivity
//                 .where((element) =>
//                     (element.isWeekly &&
//                         element.day ==
//                             allDatesOfYear[selectedIndex.value]['day']) ||
//                     (!element.isWeekly &&
//                         element.date ==
//                             allDatesOfYear[selectedIndex.value]['date']))
//                 .toList();
//
//             // Filter toilet, food, and sleep activities based on selected date
//             toiletListForDay.value = toiletList
//                 .where((element) => isSameDay(element.createdAt, selectedDate))
//                 .toList();
//
//             foodListForDay.value = foodList
//                 .where((element) => isSameDay(element.createdAt, selectedDate))
//                 .toList();
//
//             sleepListForDay.value = sleepList
//                 .where((element) => isSameDay(element.createdAt, selectedDate))
//                 .toList();
//
//             combinedActivities.value = <dynamic>[
//               ...teacherActivitiesForDay.value,
//               ...toiletListForDay.value,
//               ...foodListForDay.value,
//               ...sleepListForDay.value,
//             ];
//
//             combinedActivities.value.sort((a, b) {
//               DateTime getActivityTime(dynamic activity) {
//                 if (activity is TeacherActivityModel) {
//                   final startTime =
//                       DateFormat('HH:mm').parse(activity.startTime);
//                   return DateTime(
//                     selectedDate.year,
//                     selectedDate.month,
//                     selectedDate.day,
//                     startTime.hour,
//                     startTime.minute,
//                   );
//                 } else if (activity is ToiletModel ||
//                     activity is FoodModel ||
//                     activity is SleepModel) {
//                   return (activity.createdAt as DateTime?) ?? DateTime.now();
//                 }
//                 return DateTime.now();
//               }
//
//               return getActivityTime(a).compareTo(getActivityTime(b));
//             });
//
//             return () {};
//           }, [
//             selectedIndex.value,
//             teacherActivity,
//             toiletList,
//             foodList,
//             sleepList
//           ]);
//
//           return Column(
//             children: [
//               DateAttendanceFilterWidget(
//                 selectedDay: allDatesOfYear[selectedIndex.value]['day']!,
//                 date: allDatesOfYear[selectedIndex.value]['date']!,
//                 onNext: () {
//                   if (selectedIndex.value < allDatesOfYear.length - 1) {
//                     selectedIndex.value++;
//                   }
//                 },
//                 onPrevious: () {
//                   if (selectedIndex.value > 0) {
//                     selectedIndex.value--;
//                   }
//                 },
//               ),
//               context.mediumGap,
//               if (combinedActivities.value.isEmpty) ...[
//                 Column(
//                   children: [
//                     context.xxLargeGap,
//                     Text(
//                       context.tr.noActivities,
//                       style: context.headLine,
//                     ),
//                   ],
//                 ),
//               ] else ...[
//                 ListView.builder(
//                   physics: const NeverScrollableScrollPhysics(),
//                   shrinkWrap: true,
//                   itemCount: combinedActivities.value.length,
//                   itemBuilder: (context, index) {
//                     final activity = combinedActivities.value[index];
//                     return ParentActivityCard(
//                       activityModel: activity,
//                       selectedDate: allDatesOfYear[selectedIndex.value]
//                           ['date']!,
//                     );
//                   },
//                 ),
//               ],
//             ],
//           );
//         });
//       },
//     );
//   }
//
//   DateTime getDateFromSelectedIndex(
//       int index, List<Map<String, String>> allDatesOfYear) {
//     final selectedDateString = allDatesOfYear[index]['date']!;
//     return DateFormat('yyyy-MM-dd').parse(selectedDateString);
//   }
//
//   List<Map<String, String>> generateAllDatesOfYear() {
//     final now = DateTime.now();
//     final startOfYear = DateTime(now.year, 1, 1);
//     final endOfYear = DateTime(now.year, 12, 31);
//     final dates = <Map<String, String>>[];
//
//     for (var date = startOfYear;
//         date.isBefore(endOfYear.add(const Duration(days: 1)));
//         date = date.add(const Duration(days: 1))) {
//       dates.add({
//         'day': DateFormat('EEEE', 'en').format(date),
//         'date': DateFormat('yyyy-MM-dd').format(date),
//       });
//     }
//     return dates;
//   }
// }
