import 'dart:developer';

import 'package:connectify_app/src/screens/auth/models/user_model.dart';
import 'package:connectify_app/src/screens/auth/repos/users_repo.dart';
import 'package:connectify_app/src/screens/auth/view/sign_in_screen/sign_in_screen.dart';
import 'package:connectify_app/src/screens/class/view/classes_screen/classes_screen.dart';
import 'package:connectify_app/src/shared/extensions/context_extensions.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:restart_app/restart_app.dart';
import 'package:xr_helper/xr_helper.dart';

import '../../../shared/data/remote/api_strings.dart';
import '../../home/<USER>/main_screen.dart';
import '../../nursery/models/nursery_model.dart';

final userProviderController =
    Provider.family<UserController, BuildContext>((ref, context) {
  return UserController(context, userRepo: ref.watch(userRepoProvider));
});

final userChangeNotifierProvider =
    ChangeNotifierProvider.family<UserController, BuildContext>((ref, context) {
  return UserController(context, userRepo: ref.watch(userRepoProvider));
});

class UserController extends BaseVM {
  final UserRepo userRepo;
  final BuildContext context;

  UserController(this.context, {required this.userRepo});

  Future<void> checkUserExistAndAddOrEdit({
    required Map<String, TextEditingController> controllers,
    required String pickedImage,
    required UserTypeEnum userType,
  }) async {
    return await baseFunction(context, () async {
      final user = UserModel(
        name: controllers[ApiStrings.name]?.text ?? '',
        phone: controllers[ApiStrings.phone]?.text,
        email: controllers[ApiStrings.email]?.text,
        password: controllers[ApiStrings.password]?.text,
        userType: userType,
      );

      final signedUser = await userRepo.signedUser(phone: user.phone ?? '');

      if (signedUser?.userType == UserTypeEnum.teacher ||
          signedUser?.userType == UserTypeEnum.admin) {
        context.showBarMessage(
            context.tr.pleaseUseConnectifyAdminTeachersApplication,
            isError: true);
        return;
      }

      if (signedUser != null) {
        await editUser(
          id: signedUser.id!,
          controllers: controllers,
          pickedImage: pickedImage,
          userType: userType,
        );
      } else {
        await addUser(
          controllers: controllers,
          pickedImage: pickedImage,
        );
      }

      await login(controllers: controllers);
    });
  }

  Future<void> addUser(
      {required Map<String, TextEditingController> controllers,
      required String pickedImage}) async {
    return await baseFunction(context, () async {
      final user = UserModel(
        name: controllers[ApiStrings.name]?.text ?? '',
        phone: controllers[ApiStrings.phone]?.text,
        email: controllers[ApiStrings.email]?.text,
        password: controllers[ApiStrings.password]?.text,
      );

      await userRepo.addUser(
        user: user,
        pickedImage: pickedImage,
      );
    });
  }

  Future<void> editUser(
      {required int id,
      required Map<String, TextEditingController> controllers,
      required String pickedImage,
      required UserTypeEnum userType}) async {
    return await baseFunction(context, () async {
      final user = UserModel(
          name: controllers[ApiStrings.name]?.text ?? '',
          phone: controllers[ApiStrings.phone]?.text,
          email: controllers[ApiStrings.email]?.text,
          password: controllers[ApiStrings.password]?.text,
          userType: userType);

      await userRepo.editUser(
        user: user,
        pickedImage: pickedImage,
        id: id,
      );
    });
  }

  Future<void> deleteUser({required int id}) async {
    return await baseFunction(context, () async {
      await userRepo.deleteUser(id: id);
      context.back();
      context.toReplacement(const ClassesScreen());
      context.showBarMessage(context.tr.deletedSuccessfully);
    });
  }

  Future<void> login({
    required Map<String, TextEditingController> controllers,
  }) async {
    return await baseFunction(context, () async {
      final signedUser =
          await userRepo.signedUser(phone: controllers[ApiStrings.phone]!.text);

      if (signedUser == null) {
        context.showBarMessage(context.tr.userNotFound, isError: true);
        return;
      }

      final login = await userRepo.login(
        user: UserModel(
          email: signedUser.email,
          password: controllers[ApiStrings.password]!.text,
        ),
      );

      if (login == false) {
        context.showBarMessage(context.tr.userNotFound, isError: true);
        return;
      }

      final studentIds = signedUser?.studentIds ?? [];

      for (var student in studentIds) {
        NotificationService.subscribeToTopic('parents${student.$1}');
      }

      await userRepo.saveUserToLocal(user: signedUser);

      final nurseryData =
          await userRepo.getNurseryById(id: signedUser.nurseryModel?.id);

      final nurseryModel = NurseryModel.fromAttributesJson(
          nurseryData ?? signedUser.nurseryModel?.toDataJson() ?? {});

      log('Fefewfegw ${nurseryModel.toDataJson()}');

      await GetStorageService.setLocalData(
        key: LocalKeys.nursery,
        value: nurseryModel.toDataJson(),
      );

      GetStorageService.setLocalData(key: LocalKeys.loggedIn, value: true);

      if (kDebugMode) {
        context.toReplacement(const MainScreen());
      } else {
        Restart.restartApp();
      }
    });
  }

  Future<void> getUserById({required int id}) async {
    return await baseFunction(context, () async {
      final user = await userRepo.getUserById(id: id);

      if (user == null) {
        return;
      }

      log('afasfasf ${user.toJson()}');

      await userRepo.saveUserToLocal(user: user);
    });
  }

  Future<void> updateUser({
    required UserModel user,
  }) async {
    return await baseFunction(context, () async {
      await userRepo.updateUser(user: user);
      context.toReplacement(const SignInScreen());
      context.showBarMessage(context.tr.editSuccessfully);
    });
  }
}
