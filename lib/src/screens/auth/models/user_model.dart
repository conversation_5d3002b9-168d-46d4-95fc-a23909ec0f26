import 'package:connectify_app/src/screens/class/models/class_model.dart';
import 'package:connectify_app/src/screens/nursery/models/nursery_model.dart';
import 'package:connectify_app/src/screens/nursery/models/nursery_model_helper.dart';
import 'package:connectify_app/src/shared/shared_models/base_media_model.dart';
import 'package:connectify_app/src/shared/shared_models/base_model.dart';
import 'package:xr_helper/xr_helper.dart';

import '../../../shared/data/remote/api_strings.dart';

part 'user_model_helper.dart';

List<UserModel> responseToUserModelList(response) {
  final data = (response as List?) ?? [];

  final users = data.map((e) => UserModel.fromJson(e)).toList();

  return users;
}

class UserModel extends BaseModel {
  final String? phone;
  final UserTypeEnum? userType;
  final NurseryModel? nurseryModel;

  //? For Auth
  final String? email;
  final String? password;

  final List<ClassModel>? classes;

  final String? fcmToken;

  final List<(int? studentId, int? classId)> studentIds;

  // name without any numbers
  String get nameWithoutNumbers => name.replaceAll(RegExp(r'\d'), '');

  const UserModel({
    super.id,
    super.name,
    super.description,
    super.image,
    this.phone,
    this.classes,
    this.userType,
    this.email,
    this.password,
    this.nurseryModel,
    this.fcmToken,
    this.studentIds = const [],
  });

  static studentName() {
    final localStudentName =
        GetStorageService.getLocalData(key: LocalKeys.user);

    return localStudentName ?? '';
  }

  static int studentId() {
    final localStudentId =
        GetStorageService.getLocalData(key: LocalKeys.studentId);

    return localStudentId ?? 0;
  }

  static int classId() {
    final localClassId = GetStorageService.getLocalData(key: LocalKeys.classId);

    return localClassId ?? 0;
  }

  factory UserModel.fromJson(Map<String, dynamic> json) {
    final image = json[ApiStrings.photo] != null
        ? BaseMediaModel.fromJson(json[ApiStrings.photo])
        : null;

    // final classModel = List
    // json[ApiStrings.classString] != null
    //     ? ClassModel.fromJsonWithOutAttributes(json[ApiStrings.classString])
    //     : null;

    final nurseryModel = json[ApiStrings.nursery] != null
        ? NurseryModel.fromJson(json[ApiStrings.nursery])
        : null;

    return UserModel(
        id: json[ApiStrings.id],
        name: json[ApiStrings.username] ?? '',
        email: json[ApiStrings.email] ?? '',
        phone: json[ApiStrings.phone] ?? '',
        description: json[ApiStrings.jobTitle] ?? '',
        password: json[ApiStrings.password] ?? '',
        studentIds: json[ApiStrings.studentIds] != null
            ? (json[ApiStrings.studentIds] as List<dynamic>)
                .map<(int? studentId, int? classId)>((e) => (
                      e[ApiStrings.studentId] as int?,
                      e[ApiStrings.classId] as int?
                    ))
                .toList()
            : [],
        classes: json[ApiStrings.teacherClasses] != null
            ? (json[ApiStrings.teacherClasses] as List)
                .map<ClassModel>((e) => ClassModel.fromJsonWithOutAttributes(e))
                .toList()
            : [],
        image: image,
        nurseryModel: nurseryModel,
        fcmToken: json[ApiStrings.fcmToken],
        userType: _userTypeCheck(
          json[ApiStrings.type],
        ));
  }

  Map<String, dynamic> toJson(
      {bool isEdit = false,
      //? For Parents
      int? studentId,
      int? classId}) {
    return {
      if (id != null) ApiStrings.id: id,
      if (name.isNotEmpty) ApiStrings.username: name,
      ApiStrings.phone: phone,
      ApiStrings.type: UserTypeEnum.parent.name,
      if (studentIds.isNotEmpty)
        ApiStrings.studentIds: studentIds
            .map((e) => {
                  ApiStrings.studentId: e.$1,
                  ApiStrings.classId: e.$2,
                })
            .toList(),
      // const UserModel().currentUser.isTeacher
      //     ? ApiStrings.teacher
      //     : userType?.toString().split('.').last ?? ApiStrings.admin,
      ApiStrings.email: email,
      // '$<EMAIL>',

      if (password != null && password!.isNotEmpty)
        ApiStrings.password: password,

      // '${name.trim()}@1234',
      if (classes != null)
        ApiStrings.teacherClasses: classes?.map((e) => e.toJson()).toList(),
      if (studentId != null) ApiStrings.studentId: studentId,
      if (classId != null) ApiStrings.classId: classId,
      if (studentId != null)
        ApiStrings.nurseryId: NurseryModelHelper.currentNurseryId()
    };
  }

  //? To Login Json
  Map<String, dynamic> toLoginJson({bool isEdit = false}) {
    return {
      if (email != null && email!.isNotEmpty) ApiStrings.identifier: email,
      if (password != null && password!.isNotEmpty)
        ApiStrings.password: password,
      if (fcmToken != null && fcmToken!.isNotEmpty)
        ApiStrings.fcmToken: fcmToken,
    };
  }

  factory UserModel.empty() => const UserModel();

  UserModel copyWith({
    int? id,
    String? name,
    String? description,
    BaseMediaModel? image,
    String? phone,
    List<ClassModel>? classes,
    UserTypeEnum? userType,
    String? email,
    String? password,
    String? fcmToken,
  }) {
    return UserModel(
      id: id ?? this.id,
      name: name ?? this.name,
      description: description ?? this.description,
      image: image ?? this.image,
      phone: phone ?? this.phone,
      classes: classes ?? this.classes,
      userType: userType ?? this.userType,
      email: email ?? this.email,
      password: password ?? this.password,
      fcmToken: fcmToken ?? this.fcmToken,
    );
  }

  //? To String
  @override
  String toString() {
    return 'UserModel{id: $id, name: $name, description: $description, image: $image, phone: $phone, userType: ${userType?.name}, email: $email, password: $password, classModel: ${classes?.map((e) => e?.name).toList()}, fcmToken: $fcmToken}';
  }
}

// import 'dart:developer';
//
// import 'package:connectify_app/src/screens/class/models/class_model.dart';
// import 'package:connectify_app/src/screens/nursery/models/nursery_model.dart';
// import 'package:connectify_app/src/shared/shared_models/base_media_model.dart';
// import 'package:connectify_app/src/shared/shared_models/base_model.dart';
// import 'package:xr_helper/xr_helper.dart';
//
// import '../../../shared/data/remote/api_strings.dart';
//
// part 'user_model_helper.dart';
//
// List<UserModel> responseToUserModelList(response) {
//   final data = (response as List?) ?? [];
//
//   final users = data.map((e) => UserModel.fromJson(e)).toList();
//
//   return users;
// }
//
// class UserModel extends BaseModel {
//   final String? phone;
//   final UserTypeEnum? userType;
//   final NurseryModel? nurseryModel;
//
//   //? For Auth
//   final String? email;
//   final String? password;
//
//   final ClassModel? classModel;
//
//   const UserModel({
//     super.id,
//     super.name,
//     super.description,
//     super.image,
//     this.phone,
//     this.classModel,
//     this.userType,
//     this.email,
//     this.password,
//     this.nurseryModel,
//   });
//
//   static studentName() {
//     final localStudentName =
//         GetStorageService.getLocalData(key: LocalKeys.user);
//
//     return localStudentName ?? '';
//   }
//
//   static int studentId() {
//     final localStudentId =
//         GetStorageService.getLocalData(key: LocalKeys.studentId);
//
//     return localStudentId ?? 0;
//   }
//
//   static int classId() {
//     final localClassId = GetStorageService.getLocalData(key: LocalKeys.classId);
//
//     return localClassId ?? 0;
//   }

//   factory UserModel.fromJson(Map<String, dynamic> json) {
//     final image = json[ApiStrings.photo] != null
//         ? BaseMediaModel.fromJson(json[ApiStrings.photo])
//         : null;
//
//     final classModel = json[ApiStrings.classString] != null
//         ? ClassModel.fromJsonWithOutAttributes(json[ApiStrings.classString])
//         : null;
//
//     final nurseryModel = json[ApiStrings.nursery] != null
//         ? NurseryModel.fromJson(json[ApiStrings.nursery])
//         : null;
//
//     log('asfasfsffffa ${nurseryModel?.showNurseryLogoInParentApp}');
//
//     return UserModel(
//         id: json[ApiStrings.id],
//         name: json[ApiStrings.username] ?? '',
//         email: json[ApiStrings.email] ?? '',
//         phone: json[ApiStrings.phone] ?? '',
//         description: json[ApiStrings.jobTitle] ?? '',
//         password: json[ApiStrings.password] ?? '',
//         classModel: classModel,
//         image: image,
//         nurseryModel: nurseryModel,
//         userType: _userTypeCheck(
//           json[ApiStrings.type],
//         ));
//   }
//
//   Map<String, dynamic> toJson({bool isEdit = false}) {
//     return {
//       if (id != null) ApiStrings.id: id,
//       if (name.isNotEmpty) ApiStrings.username: name,
//       ApiStrings.phone: phone,
//       ApiStrings.type: 'parent',
//       // userType?.toString().split('.').last ?? ApiStrings.admin,
//       ApiStrings.email: email,
//       // '$<EMAIL>',
//
//       if (password != null && password!.isNotEmpty)
//         ApiStrings.password: password,
//
//       // '${name.trim()}@1234',
//       if (classModel != null) ApiStrings.classString: classModel?.toJson(),
//     };
//   }
//
//   //? To Login Json
//   Map<String, dynamic> toLoginJson({bool isEdit = false}) {
//     return {
//       ApiStrings.identifier: email,
//       ApiStrings.password: password,
//     };
//   }
//
//   factory UserModel.empty() => const UserModel();
//
//   UserModel copyWith({
//     int? id,
//     String? name,
//     String? description,
//     BaseMediaModel? image,
//     String? phone,
//     ClassModel? classModel,
//     UserTypeEnum? userType,
//     String? email,
//     String? password,
//   }) {
//     return UserModel(
//       id: id ?? this.id,
//       name: name ?? this.name,
//       description: description ?? this.description,
//       image: image ?? this.image,
//       phone: phone ?? this.phone,
//       classModel: classModel ?? this.classModel,
//       userType: userType ?? this.userType,
//       email: email ?? this.email,
//       password: password ?? this.password,
//     );
//   }
//
//   //? To String
//   @override
//   String toString() {
//     return 'UserModel{id: $id, name: $name, description: $description, image: $image, phone: $phone, userType: ${userType?.name}, email: $email, password: $password, classModel: ${classModel?.toJson()}';
//   }
// }
