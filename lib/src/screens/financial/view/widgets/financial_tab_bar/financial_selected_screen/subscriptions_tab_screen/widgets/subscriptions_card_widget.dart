import 'package:collection/collection.dart';
import 'package:connectify_app/src/screens/financial/view/financial_screen.dart';
import 'package:connectify_app/src/screens/nursery/models/nursery_model_helper.dart';
import 'package:connectify_app/src/screens/student/controllers/student_controller.dart';
import 'package:connectify_app/src/screens/student/models/student_model.dart';
import 'package:connectify_app/src/shared/consts/app_constants.dart';
import 'package:connectify_app/src/shared/extensions/context_extensions.dart';
import 'package:connectify_app/src/shared/services/notifications/local_notifications_service.dart';
import 'package:connectify_app/src/shared/widgets/base_container.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:quickalert/models/quickalert_type.dart';
import 'package:quickalert/widgets/quickalert_dialog.dart';
import 'package:xr_helper/xr_helper.dart';

class SubscriptionsCardWidget extends ConsumerWidget {
  final StudentModel student;
  final bool isPaidTab;
  final ValueNotifier<List<StudentModel>> paidStudents;
  final ValueNotifier<List<StudentModel>> unPaidStudents;
  final Function setState;

  const SubscriptionsCardWidget({
    super.key,
    required this.student,
    required this.isPaidTab,
    required this.paidStudents,
    required this.unPaidStudents,
    required this.setState,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final studentCtrl = ref.read(studentChangeNotifierProvider(context));

    final nextPaymentDate = student.subscriptionDate?.nextPaymentDate;

    final currentPeriodSubscription = nextPaymentDate != null
        ? student.subscriptions.firstWhereOrNull(
            (element) {
              final paymentDate = element.date.formatStringToDateTime;
              return paymentDate.year == nextPaymentDate.year &&
                  paymentDate.month == nextPaymentDate.month &&
                  element.isPaid;
            },
          )
        : null;

    // Check for pending payment request (has screenshot but not approved)
    // Only show pending if there's no paid subscription for the current period
    final pendingPaymentRequest =
        nextPaymentDate != null && currentPeriodSubscription == null
            ? student.subscriptions.lastWhereOrNull(
                (element) {
                  return !element.isPaid &&
                      !element.isApproved &&
                      element.paymentScreenshot != null;
                },
              )
            : null;

    return Stack(
      alignment: Alignment.topRight,
      children: [
        BaseContainer(
            padding: AppSpaces.appbarPadding,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    //! Subscription (Name - Date)
                    Row(
                      children: [
                        SizedBox(
                          height: 50.h,
                          width: 50.w,
                          child: ClipRRect(
                            borderRadius: BorderRadius.circular(
                                AppRadius.baseContainerRadius),
                            child: Image.network(
                              student.image?.url ?? '',
                              height: 50.h,
                              width: 50.w,
                              fit: BoxFit.cover,
                              errorBuilder: (context, error, stackTrace) =>
                                  Image.network(
                                AppConsts.studentPlaceholder,
                              ),
                            ),
                          ),
                        ),
                        context.xSmallGap,
                        Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            //! Subscription Name
                            Text(
                              student.name,
                              style: context.blueHint
                                  .copyWith(fontWeight: FontWeight.bold),
                            ),

                            context.xSmallGap,

                            //! Subscription Date or Status
                            Text(
                              student.subscriptionDate == null
                                  ? context.tr.noSubscriptionDateSet
                                  : isPaidTab &&
                                          currentPeriodSubscription != null
                                      ? '${context.tr.paid}: ${currentPeriodSubscription.date}'
                                      : pendingPaymentRequest != null
                                          ? context.tr.paymentPendingApproval
                                          : nextPaymentDate != null
                                              ? '${context.tr.due}: ${nextPaymentDate.formatDateToString}'
                                              : context
                                                  .tr.subscriptionDateNeeded,
                              style: context.hint.copyWith(
                                fontSize: 12,
                                color: student.subscriptionDate == null
                                    ? Colors.red
                                    : isPaidTab
                                        ? Colors.green
                                        : pendingPaymentRequest != null
                                            ? Colors.blue
                                            : Colors.orange,
                              ),
                            ),
                            // Show payment method if there's a pending request
                            if (pendingPaymentRequest?.paymentMethod !=
                                null) ...[
                              context.xSmallGap,
                              Text(
                                '${context.tr.paymentMethod}: ${pendingPaymentRequest!.paymentMethod}',
                                style: context.hint.copyWith(
                                  fontSize: 11,
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                            ],
                          ],
                        ),
                      ],
                    ),

                    const Spacer(),

                    //! Subscription Price

                    Text(
                      // isPaidTab
                      //     ? ' \$${currentPeriodSubscription?.amount ?? 0}'
                      //     :
                      ' \$${student.fees ?? NurseryModelHelper.currentNursery()?.fees ?? 0}',
                      style: context.priceTitle,
                    ),

                    context.mediumGap,

                    if (!isPaidTab)
                      IconButton(
                        onPressed: () {
                          // Check if student has subscription date
                          if (student.subscriptionDate == null) {
                            QuickAlert.show(
                              context: context,
                              title: context.tr.subscriptionDateRequired,
                              text: context
                                  .tr.pleaseAddTheStudentSubscriptionDateFirst,
                              type: QuickAlertType.warning,
                              confirmBtnText: 'OK',
                              confirmBtnColor: ColorManager.buttonColor,
                            );
                            return;
                          }

                          final nextPaymentDate =
                              student.subscriptionDate!.nextPaymentDate;
                          if (nextPaymentDate == null) {
                            QuickAlert.show(
                              context: context,
                              title: context.tr.errorOccurred,
                              text: context.tr.unableToCalculateNextPaymentDate,
                              type: QuickAlertType.error,
                              confirmBtnText: 'OK',
                              confirmBtnColor: ColorManager.buttonColor,
                            );
                            return;
                          }

                          QuickAlert.show(
                            context: context,
                            title: context.tr.warning,
                            text: context.tr
                                .areYouSureToMakeThisSubscriptionPaidFor(
                              nextPaymentDate.formatDateToString,
                            ),
                            type: QuickAlertType.info,
                            confirmBtnText: context.tr.confirm,
                            cancelBtnText: context.tr.cancel,
                            showCancelBtn: true,
                            confirmBtnColor: ColorManager.buttonColor,
                            onConfirmBtnTap: () async {
                              Navigator.of(context).pop();

                              List<SubscriptionModel> subscriptions;

                              // Check if there's a pending payment request to approve
                              if (pendingPaymentRequest != null) {
                                // Update the existing pending request to approved and paid
                                subscriptions =
                                    student.subscriptions.map((subscription) {
                                  if (subscription == pendingPaymentRequest) {
                                    return subscription.copyWith(
                                      isPaid: true,
                                      isApproved: true,
                                    );
                                  }
                                  return subscription;
                                }).toList();

                                // Add a new subscription for the next payment date
                                subscriptions.add(
                                  SubscriptionModel(
                                    amount: student.fees ??
                                        NurseryModelHelper.currentNursery()
                                            ?.fees ??
                                        0,
                                    date: nextPaymentDate.formatDateToString,
                                    isPaid: true,
                                    isApproved: true,
                                  ),
                                );
                              } else {
                                // Create a new subscription if no pending request exists
                                subscriptions = [
                                  ...student.subscriptions,
                                  SubscriptionModel(
                                      amount: student.fees ??
                                          NurseryModelHelper.currentNursery()
                                              ?.fees ??
                                          0,
                                      date: nextPaymentDate.formatDateToString,
                                      isPaid: true,
                                      isApproved: true),
                                ];
                              }

                              await studentCtrl.paySubscription(
                                studentId: student.id!,
                                subscriptions: subscriptions,
                                navigateWidget: const FinancialScreen(),
                              );

                              final copiedStudent = student.copyWith(
                                subscriptions: subscriptions,
                              );

                              // Schedule next month's subscription reminder
                              if (copiedStudent.subscriptionDate != null) {
                                await LocalNotificationsService
                                    .rescheduleSubscriptionReminder(
                                  student: copiedStudent,
                                  title: context.tr.subscriptionReminderTitle,
                                  body: context.tr.subscriptionReminderBody(
                                      copiedStudent.name),
                                );
                              }

                              context
                                  .showBarMessage(context.tr.paidSuccessfully);

                              paidStudents.value.insert(0, copiedStudent);
                              unPaidStudents.value.removeWhere(
                                (element) => element.id == student.id,
                              );

                              setState(() {});
                            },
                          );
                        },
                        icon: const Icon(
                          Icons.done_all,
                          color: ColorManager.buttonColor,
                        ),
                      ),
                  ],
                ),

                // Payment Screenshot Section
                if (pendingPaymentRequest?.paymentScreenshot != null) ...[
                  context.mediumGap,
                  Divider(
                    color: Colors.grey.shade300,
                  ),
                  context.xSmallGap,
                  Text(
                    '${context.tr.paymentScreenshot}:',
                    style: context.hint.copyWith(
                      fontSize: 12,
                      fontWeight: FontWeight.w600,
                      color: Colors.blue,
                    ),
                  ),
                  context.xSmallGap,
                  GestureDetector(
                    onTap: () {
                      // Show full screen image
                      showDialog(
                        context: context,
                        builder: (context) => Dialog(
                          child: Image.network(
                            pendingPaymentRequest?.paymentScreenshot?.url ?? '',
                            fit: BoxFit.contain,
                            errorBuilder: (context, error, stackTrace) =>
                                const Center(
                              child: Icon(
                                Icons.error,
                                size: 50,
                                color: Colors.red,
                              ),
                            ),
                          ),
                        ),
                      );
                    },
                    child: Container(
                      height: 120.h,
                      width: double.infinity,
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(8),
                        border: Border.all(color: Colors.grey.shade300),
                      ),
                      child: ClipRRect(
                        borderRadius: BorderRadius.circular(8),
                        child: Image.network(
                          pendingPaymentRequest?.paymentScreenshot?.url ?? '',
                          fit: BoxFit.cover,
                          errorBuilder: (context, error, stackTrace) =>
                              const Center(
                            child: Icon(
                              Icons.error,
                              size: 30,
                              color: Colors.red,
                            ),
                          ),
                        ),
                      ),
                    ),
                  ),
                ],
              ],
            ).paddingOnly(
              right: AppSpaces.mediumPadding,
            )),
      ],
    );
  }

  void showAcceptSubscriptionDialog(
    BuildContext context, {
    required WidgetRef ref,
  }) {}
}
