import 'package:connectify_app/src/screens/auth/models/user_model.dart';
import 'package:connectify_app/src/screens/nursery/models/nursery_model.dart';
import 'package:connectify_app/src/screens/student/models/student_model.dart';
import 'package:connectify_app/src/screens/teacher/models/teacher_model.dart';
import 'package:connectify_app/src/shared/data/remote/api_strings.dart';
import 'package:equatable/equatable.dart';

import '../../nursery/models/nursery_model_helper.dart';

List<MessageModel> responseToMessageModelList(response) {
  final data = (response[ApiStrings.data] as List?) ?? [];

  final messageData = data.map((e) => MessageModel.fromJson(e)).toList();

  return messageData;
}

class MessageModel extends Equatable {
  final int? id;
  final String? title;
  final String? description;
  final String? createdAt;
  final StudentModel? student;
  final NurseryModel? nursery;
  final TeacherModel? teacher;
  final UserModel? admin;
  final String? type;

  const MessageModel({
    this.id,
    this.title,
    this.description,
    this.createdAt,
    this.student,
    this.nursery,
    this.teacher,
    this.admin,
    this.type,
  });

  factory MessageModel.fromJson(Map<String, dynamic> json) {
    final attributes = json[ApiStrings.attributes];

    return MessageModel(
      id: json[ApiStrings.id],
      type: attributes[ApiStrings.type],
      title: attributes[ApiStrings.title],
      description: attributes[ApiStrings.description],
      nursery: attributes[ApiStrings.nursery] != null
          ? NurseryModel.fromJson(attributes[ApiStrings.nursery])
          : null,
      teacher: attributes[ApiStrings.teacher] != null &&
              attributes[ApiStrings.teacher]['data'] != null
          ? TeacherModel.fromJson(attributes[ApiStrings.teacher]['data'])
          : null,
      admin: attributes[ApiStrings.admin] != null
          ? UserModel.fromJson(attributes[ApiStrings.admin])
          : null,
      createdAt: attributes[ApiStrings.createdAt],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      if (id != null) ApiStrings.id: id,
      ApiStrings.title: title,
      ApiStrings.description: description,
      ApiStrings.student: UserModel.studentId(),
      ApiStrings.teacher: teacher?.id,
      ApiStrings.admin: admin?.id,
      ApiStrings.type: ApiStrings.parent,
      ApiStrings.nursery: NurseryModelHelper.currentNurseryId()
    };
  }

  @override
  List<Object?> get props =>
      [id, title, description, student, nursery, teacher, admin];
}
