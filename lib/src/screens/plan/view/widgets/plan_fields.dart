import 'package:connectify_app/src/screens/class/models/class_model.dart';
import 'package:connectify_app/src/shared/extensions/context_extensions.dart';
import 'package:connectify_app/src/shared/widgets/drop_downs/class_drop_down.dart';
import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:xr_helper/xr_helper.dart';

class PlanFields extends HookWidget {
  final Map<String, TextEditingController> controllers;
  final ValueNotifier<ClassModel?> selectedClass;
  final ValueNotifier<List<Map<String, TextEditingController>>>
      sectionControllers;

  const PlanFields({
    super.key,
    required this.controllers,
    required this.selectedClass,
    required this.sectionControllers,
  });

  @override
  Widget build(BuildContext context) {
    final focusNode = useFocusNode();

    return SingleChildScrollView(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Class dropdown
          ClassDropDown(
            selectedClass: selectedClass,
            selectFirstClass: false,
          ),

          context.fieldsGap,

          // Sections header
          Text(
            context.tr.sections,
            style: context.labelLarge?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),

          context.smallGap,

          // Sections list
          ValueListenableBuilder<List<Map<String, TextEditingController>>>(
            valueListenable: sectionControllers,
            builder: (context, controllers, child) {
              return Column(
                children: [
                  ...controllers.asMap().entries.map((entry) {
                    final index = entry.key;
                    final sectionController = entry.value;
                    final isLastItem = index == controllers.length - 1;

                    return Container(
                      margin: const EdgeInsets.only(bottom: 16),
                      padding: const EdgeInsets.all(16),
                      decoration: BoxDecoration(
                        border: Border.all(color: Colors.grey.shade300),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          // Section header
                          Row(
                            children: [
                              Text(
                                '${context.tr.section} ${index + 1}',
                                style: context.labelMedium?.copyWith(
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                              const Spacer(),
                              IconButton(
                                icon: const Icon(Icons.remove_circle,
                                    color: Colors.red),
                                onPressed: () {
                                  sectionControllers.value =
                                      List.from(sectionControllers.value)
                                        ..removeAt(index);
                                },
                              ),
                            ],
                          ),

                          context.smallGap,

                          // Section title
                          BaseTextField(
                            focusNode: isLastItem ? focusNode : null,
                            controller: sectionController['title']!,
                            isRequired: false,
                            label: context.tr.sectionTitle,
                            hint: context.tr.enterSectionTitle,
                            onChanged: (value) {
                              // No need to update a separate list since controllers hold the values
                            },
                          ),

                          context.fieldsGap,

                          // Section description
                          BaseTextField(
                            controller: sectionController['description']!,
                            isRequired: false,
                            label: context.tr.sectionDescription,
                            hint: context.tr.enterSectionDescription,
                            textInputType: TextInputType.multiline,
                            maxLines: 3,
                            onChanged: (value) {
                              // No need to update a separate list since controllers hold the values
                            },
                          ),
                        ],
                      ),
                    );
                  }),
                  TextButton(
                    onPressed: () {
                      sectionControllers.value = [
                        ...sectionControllers.value,
                        {
                          'title': TextEditingController(text: ''),
                          'description': TextEditingController(text: ''),
                        },
                      ];

                      WidgetsBinding.instance.addPostFrameCallback((_) {
                        focusNode.requestFocus();
                      });
                    },
                    child: Row(
                      children: [
                        const Icon(Icons.add).decorated(
                          border: Border.all(
                            color: ColorManager.primaryColor,
                          ),
                        ),
                        const SizedBox(width: 8),
                        Text(context.tr.addNewSection),
                      ],
                    ),
                  ),
                ],
              );
            },
          ),
        ],
      ),
    );
  }
}
