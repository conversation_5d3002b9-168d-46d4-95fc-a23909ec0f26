import 'package:connectify_app/src/screens/class/models/class_model.dart';
import 'package:connectify_app/src/screens/plan/controller/plan_controller.dart';
import 'package:connectify_app/src/screens/plan/model/plan_model.dart';
import 'package:connectify_app/src/screens/plan/view/widgets/plan_fields.dart';
import 'package:connectify_app/src/shared/extensions/context_extensions.dart';
import 'package:connectify_app/src/shared/widgets/loading/loading_widget.dart';
import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:xr_helper/xr_helper.dart';

import '../../home/<USER>/main_screen/widgets/main_app_bar.dart';

class AddPlanScreen extends HookConsumerWidget {
  final PlanModel? plan;

  const AddPlanScreen({super.key, this.plan});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final planCtrl = ref.watch(planChangeNotifierControllerProvider(context));

    final formKey = useState(GlobalKey<FormState>());

    final controllers = <String, TextEditingController>{};

    final selectedClass = useState<ClassModel?>(plan?.classModel);

    // Initialize section controllers with existing plan sections or one empty section
    final sectionControllers =
        useState<List<Map<String, TextEditingController>>>(
      plan?.sections.isNotEmpty == true
          ? plan!.sections
              .map((section) => {
                    'title': TextEditingController(text: section.title),
                    'description':
                        TextEditingController(text: section.description),
                  })
              .toList()
          : [
              {
                'title': TextEditingController(text: ''),
                'description': TextEditingController(text: ''),
              }
            ],
    );

    Future<void> handleSubmit() async {
      if (selectedClass.value == null) {
        context.showBarMessage(context.tr.selectClass, isError: true);
        return;
      }

      if (formKey.value.currentState?.validate() ?? false) {
        // Convert controllers to sections and filter out empty ones
        final validSections = sectionControllers.value
            .map((controllers) => PlanSection(
                  title: controllers['title']!.text.trim(),
                  description: controllers['description']!.text.trim(),
                ))
            .where((section) =>
                section.title.isNotEmpty || section.description.isNotEmpty)
            .toList();

        if (validSections.isEmpty) {
          context.showBarMessage(context.tr.pleaseAddAtLeastOneSection,
              isError: true);
          return;
        }

        if (plan != null) {
          // Edit existing plan
          await planCtrl.editPlan(
            id: plan!.id!,
            sections: validSections,
            classModel: selectedClass.value,
          );
        } else {
          // Add new plan
          await planCtrl.addPlan(
            sections: validSections,
            classModel: selectedClass.value,
          );
        }
      }
    }

    return Scaffold(
      appBar: MainAppBar(
        isBackButton: true,
        title: plan != null ? context.tr.editPlan : context.tr.addPlan,
        iconPath: '',
      ),
      body: Padding(
        padding: const EdgeInsets.all(AppSpaces.mediumPadding),
        child: Form(
          key: formKey.value,
          child: Column(
            children: [
              Expanded(
                child: PlanFields(
                  controllers: controllers,
                  selectedClass: selectedClass,
                  sectionControllers: sectionControllers,
                ),
              ),
              context.largeGap,
              Button(
                loadingWidget: const LoadingWidget(),
                isLoading: planCtrl.isLoading,
                onPressed: handleSubmit,
                label: plan != null ? context.tr.update : context.tr.add,
              ),
            ],
          ),
        ),
      ),
    );
  }
}
