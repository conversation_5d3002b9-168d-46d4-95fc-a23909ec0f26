import 'package:connectify_app/src/screens/auth/models/user_model.dart';
import 'package:connectify_app/src/screens/class/models/class_model.dart';
import 'package:connectify_app/src/screens/nursery/models/nursery_model_helper.dart';
import 'package:connectify_app/src/screens/teacher/models/teacher_model.dart';
import 'package:connectify_app/src/shared/data/remote/api_strings.dart';
import 'package:equatable/equatable.dart';
import 'package:xr_helper/xr_helper.dart';

List<PlanModel> responseToPlanModelList(response) {
  final data = (response[ApiStrings.data] as List?) ?? [];

  final plans = data.map((e) => PlanModel.fromJson(e)).toList();

  return plans;
}

class PlanSection extends Equatable {
  final String title;
  final String description;

  const PlanSection({
    this.title = '',
    this.description = '',
  });

  factory PlanSection.fromJson(Map<String, dynamic> json) {
    return PlanSection(
      title: json[ApiStrings.title] ?? '',
      description: json[ApiStrings.subtitle] ?? '',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      ApiStrings.title: title,
      ApiStrings.subtitle: description,
    };
  }

  PlanSection copyWith({
    String? title,
    String? description,
  }) {
    return PlanSection(
      title: title ?? this.title,
      description: description ?? this.description,
    );
  }

  @override
  List<Object?> get props => [title, description];
}

class PlanModel extends Equatable {
  final int? id;
  final List<PlanSection> sections;
  final ClassModel? classModel;
  final String? date;
  final DateTime? createdAt;

  const PlanModel({
    this.id,
    this.sections = const [],
    this.classModel,
    this.date,
    this.createdAt,
  });

  factory PlanModel.fromJson(Map<String, dynamic> json) {
    final attributes = json[ApiStrings.attributes];

    final teacher = attributes[ApiStrings.teacher] != null &&
            attributes[ApiStrings.teacher][ApiStrings.data] != null
        ? TeacherModel.fromAttributesJson(
            attributes[ApiStrings.teacher][ApiStrings.data])
        : null;

    final classModel = attributes[ApiStrings.classString] != null &&
            attributes[ApiStrings.classString][ApiStrings.data] != null
        ? ClassModel.fromJson(
            attributes[ApiStrings.classString][ApiStrings.data])
        : null;

    final sectionsData = attributes[ApiStrings.sections] as List? ?? [];
    final sections =
        sectionsData.map((section) => PlanSection.fromJson(section)).toList();

    return PlanModel(
      id: json[ApiStrings.id],
      sections: sections,
      classModel: classModel,
      date: attributes[ApiStrings.date],
      createdAt: attributes[ApiStrings.createdAt] != null
          ? DateTime.parse(attributes[ApiStrings.createdAt]).toLocal()
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      if (id != null) ApiStrings.id: id,
      ApiStrings.sections: sections.map((section) => section.toJson()).toList(),
      ApiStrings.classString: classModel?.id,
      ApiStrings.teacher: const UserModel().currentUser.id,
      ApiStrings.nursery: NurseryModelHelper.currentNurseryId(),
      if (id == null) ApiStrings.date: DateTime.now().formatToMonthName,
    };
  }

  PlanModel copyWith({
    int? id,
    List<PlanSection>? sections,
    ClassModel? classModel,
    TeacherModel? teacher,
    String? date,
    DateTime? createdAt,
  }) {
    return PlanModel(
      id: id ?? this.id,
      sections: sections ?? this.sections,
      classModel: classModel ?? this.classModel,
      date: date ?? this.date,
      createdAt: createdAt ?? this.createdAt,
    );
  }

  @override
  List<Object?> get props => [
        id,
        sections,
        classModel,
        date,
        createdAt,
      ];
}
