import 'package:collection/collection.dart';
import 'package:connectify_app/src/screens/exam/controllers/exams_controller.dart';
import 'package:connectify_app/src/screens/home/<USER>/main_screen/widgets/main_app_bar.dart';
import 'package:connectify_app/src/screens/student/models/student_model.dart';
import 'package:connectify_app/src/shared/extensions/context_extensions.dart';
import 'package:connectify_app/src/shared/extensions/riverpod_extensions.dart';
import 'package:connectify_app/src/shared/widgets/base_list/base_list.dart';
import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:xr_helper/xr_helper.dart';

import '../widgets/exam_tab_bar/exam_date_filter_widget.dart';
import 'widgets/student_result_card_widget.dart';

class StudentResultDetailsScreen extends HookConsumerWidget {
  final StudentModel student;

  const StudentResultDetailsScreen({
    super.key,
    required this.student,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final selectedDate = useState<DateTime>(DateTime.now());

    final params = (context, selectedDate.value.formatDateToString);

    final getResultsStudentsFuture =
        ref.watch(getExamsDataByMonthProvider(params));

    return Scaffold(
      appBar: MainAppBar(
        title: context.isEng
            ? '${student.name} ${DateTime.now().formatToMonthName} ${context.tr.results}'
            : 'نتائج ${student.name} ل${DateTime.now().formatToMonthName} ',
        // : 'نتائج ${student.name}',
        isBackButton: true,
        iconPath: '',
      ),
      body: Column(
        children: [
          context.mediumGap,
          ExamDateFilterWidget(
            date: selectedDate.value,
            onNext: () {
              selectedDate.value = DateTime(
                selectedDate.value.year,
                selectedDate.value.month + 1,
              );
            },
            onPrevious: () {
              selectedDate.value = DateTime(
                selectedDate.value.year,
                selectedDate.value.month - 1,
              );
            },
          ),
          context.mediumGap,
          Expanded(
            child: getResultsStudentsFuture.get(
              data: (exams) {
                return HookBuilder(builder: (context) {
                  final controllers = List.generate(
                      exams.length,
                      (index) => useTextEditingController(
                            text: exams[index]
                                .studentsResult
                                .firstWhereOrNull((element) =>
                                    element.student?.id == student.id)
                                ?.note,
                          ));

                  final ratings = List.generate(exams.length, (index) {
                    final studentRate = exams[index]
                        .studentsResult
                        .firstWhereOrNull(
                            (element) => element.student?.id == student.id)
                        ?.rate;
                    return useState<num>(studentRate ?? 0.0);
                  });

                  final canAddNote = List.generate(exams.length, (index) {
                    final note = exams[index]
                        .studentsResult
                        .firstWhereOrNull(
                            (element) => element.student?.id == student.id)
                        ?.note;

                    final canAdd = note != null && note.isNotEmpty;

                    return useState<bool>(canAdd);
                  });

                  return BaseList(
                    padding: const EdgeInsets.all(AppSpaces.mediumPadding),
                    data: exams,
                    emptyWidget: Center(
                      child: Text(
                        context.tr.noQuestions,
                        style: context.subHeadLine,
                      ),
                    ),
                    separatorGap: context.mediumGap,
                    itemBuilder: (exam, index) {
                      final studentResult = exams[index]
                          .studentsResult
                          .firstWhereOrNull(
                              (element) => element.student?.id == student.id);

                      return StudentResultCardWidget(
                        exam: exam,
                        number: index + 1,
                        controller: controllers[index],
                        rating: ratings[index],
                        studentResult: studentResult,
                        canAddNote: canAddNote[index],
                      );
                    },
                    mainAxisSpacing: 15.h,
                    crossAxisSpacing: 10,
                  );
                });
              },
            ),
          ),
        ],
      ),
    );
  }
}
