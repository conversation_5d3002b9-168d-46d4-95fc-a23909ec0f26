import '../../../shared/data/remote/api_strings.dart';

List<AttendanceModel> responseToAttendanceModelList(response) {
  final data = (response[ApiStrings.data] as List?) ?? [];

  final attendance = data.map((e) => AttendanceModel.fromJson(e)).toList();

  return attendance;
}

class AttendanceModel {
  final int? id;
  final String attendanceDate;

  const AttendanceModel({
    this.id,
    this.attendanceDate = '',
  });

  //? From Json
  factory AttendanceModel.fromJson(Map<String, dynamic> json) {
    final attributes = json[ApiStrings.attributes];

    return AttendanceModel(
      id: json[ApiStrings.id],
      attendanceDate: attributes[ApiStrings.attendanceDate],
    );
  }
}
