import 'dart:developer';

import 'package:connectify_app/src/screens/auth/models/user_model.dart';
import 'package:connectify_app/src/shared/consts/app_constants.dart';
import 'package:xr_helper/xr_helper.dart';

import '../../../shared/data/remote/api_strings.dart';
import '../../../shared/shared_models/base_media_model.dart';

class NurseryModel extends UserModel {
  final int maxStudents;
  final UserModel? admin;
  final bool canContactTeacher;
  final bool showNurseryLogoInParentApp;
  final DateTime? endDate;

  const NurseryModel({
    super.id,
    super.name,
    super.image,
    this.admin,
    this.maxStudents = AppConsts.maxStudents,
    this.canContactTeacher = true,
    this.showNurseryLogoInParentApp = false,
    this.endDate,
  });

  factory NurseryModel.fromAttributesJson(Map<String, dynamic> json) {
    final attributes = json[ApiStrings.attributes];

    final logo = attributes != null &&
            attributes.containsKey(ApiStrings.logo) &&
            attributes[ApiStrings.logo][ApiStrings.data] != null
        ? BaseMediaModel.fromJson(
            attributes[ApiStrings.logo][ApiStrings.data][ApiStrings.attributes])
        : null;

    return NurseryModel(
      id: json[ApiStrings.id],
      name: attributes != null ? (attributes[ApiStrings.name] ?? '') : '',
      maxStudents: attributes[ApiStrings.maxStudents] != null
          ? (attributes[ApiStrings.maxStudents])
          : AppConsts.maxStudents,
      image: logo,
      canContactTeacher: attributes[ApiStrings.canContactTeacher] ?? true,
      showNurseryLogoInParentApp:
          attributes[ApiStrings.showNurseryLogoInParentApp] ?? false,
      admin: attributes[ApiStrings.admin] != null
          ? UserModel.fromJson(attributes[ApiStrings.admin])
          : null,
      endDate: attributes[ApiStrings.endDate] != null
          ? DateTime.parse(attributes[ApiStrings.endDate])
          : null,
    );
  }

  factory NurseryModel.fromJson(Map<String, dynamic> attributes) {
    final logo = attributes[ApiStrings.logo] != null
        ? BaseMediaModel.fromJson(attributes[ApiStrings.logo])
        : null;

    return NurseryModel(
      id: attributes[ApiStrings.id],
      maxStudents: attributes[ApiStrings.maxStudents] ?? AppConsts.maxStudents,
      admin: attributes[ApiStrings.admin] != null
          ? UserModel.fromJson(attributes[ApiStrings.admin])
          : null,
      name: attributes[ApiStrings.name] ?? '',
      canContactTeacher: attributes[ApiStrings.canContactTeacher] ?? true,
      showNurseryLogoInParentApp:
          attributes[ApiStrings.showNurseryLogoInParentApp] ?? false,
      image: logo,
      endDate: attributes[ApiStrings.endDate] != null
          ? DateTime.parse(attributes[ApiStrings.endDate])
          : null,
    );
  }

  //? Get Nursery ID
  static NurseryModel? currentNursery() {
    final nursery = GetStorageService.getLocalData(key: LocalKeys.nursery);

    if (nursery == null) return null;

    if (nursery.containsKey('attributes')) {
      return NurseryModel.fromAttributesJson(nursery);
    }

    if (nursery.containsKey('data')) {
      return NurseryModel.fromAttributesJson(nursery['data']);
    }

    // final nurseryModel =
    //     nursery['data'] is List ? nursery['data'][0] : nursery['data'];

    return NurseryModel.fromJson(nursery);
  }

  //? Get Nursery ID
  static currentNurseryId() {
    final nursery = GetStorageService.getLocalData(key: LocalKeys.nursery);

    log('asfasfasf $nursery');

    if (nursery == null) return null;

    int? nurseryId;

    // final firstNursery =
    //     nursery['data'] is List ? nursery['data'][0] : nursery['data'];

    log('asfasfasf $nursery');

    nurseryId = nursery['id'];

    // log('asfasffffffasf $nurseryId');

    return nurseryId;
  }

  // to json
  Map<String, dynamic> toDataJson() {
    return {
      ApiStrings.id: id,
      ApiStrings.name: name,
      ApiStrings.logo: image?.toJson(),
      ApiStrings.maxStudents: maxStudents,
      ApiStrings.canContactTeacher: canContactTeacher,
      ApiStrings.showNurseryLogoInParentApp: showNurseryLogoInParentApp,
    };
  }
}
