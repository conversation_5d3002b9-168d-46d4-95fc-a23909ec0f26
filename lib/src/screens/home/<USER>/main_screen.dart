import 'package:connectify_app/src/screens/home/<USER>/bottom_nav_controller.dart';
import 'package:connectify_app/src/screens/home/<USER>/home_screen/home_screen.dart';
import 'package:connectify_app/src/screens/home/<USER>/main_screen/widgets/floating_button.dart';
import 'package:connectify_app/src/screens/nursery/models/nursery_model_helper.dart';
import 'package:connectify_app/src/screens/supplies/view/admin_supplies/supplies_screen.dart';
import 'package:connectify_app/src/shared/extensions/context_extensions.dart';
import 'package:connectify_app/src/shared/widgets/shared_widgets.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:xr_helper/xr_helper.dart';

import '../../../shared/widgets/home_navigations/bottom_nav_bar_widget.dart';
import '../../messages/view/messages_screen.dart';
import 'main_screen/widgets/main_app_bar.dart';

class MainScreen extends ConsumerWidget {
  const MainScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final currentIndex = ref.watch(bottomNavigationControllerProvider);

    return SafeArea(
      child: WillPopScope(
        onWillPop: () async {
          Navigator.pushAndRemoveUntil;
          return true;
        },
        child: Scaffold(
          // backgroundColor: Color(0xffF7F7F7),
          appBar: MainAppBar(
            isHome: currentIndex == 0 ? true : false,
            title: selectedTitle(currentIndex, context),
            titleWidget: NurseryModelHelper.currentNursery()
                        ?.showNurseryLogoInParentApp ==
                    true
                ? Row(
                    children: [
                      BaseCachedImage(
                        NurseryModelHelper.currentNursery()?.image?.url ?? '',
                        width: 50,
                        height: 50,
                        radius: 12,
                      ),
                      context.smallGap,
                      Text(
                        NurseryModelHelper.currentNursery()?.name ?? '',
                        style: context.whiteTitle,
                      ),
                    ],
                  )
                : null,
          ),
          body: _SelectedScreen(
            currentIndex: currentIndex,
          ),
          bottomNavigationBar: const BottomNavBarWidget(),
          floatingActionButtonAnimator: FloatingActionButtonAnimator.scaling,
          floatingActionButtonLocation:
              FloatingActionButtonLocation.miniCenterDocked,
          floatingActionButton: const FloatingButtonWidget(),
        ),
      ),
    );
  }
}

String selectedTitle(int currentIndex, BuildContext context) {
  final bool isAfternoon = DateTime.now().hour >= 12;

  switch (currentIndex) {
    case 0:
      return isAfternoon ? context.tr.goodAfternoon : context.tr.goodMorning;
    case 1:
      return context.tr.supplies;
    case 3:
      return context.tr.messages;
    case 4:
      return context.tr.settings;
  }
  return context.tr.goodMorning;
}

class _SelectedScreen extends StatelessWidget {
  final int currentIndex;

  const _SelectedScreen({required this.currentIndex});

  @override
  Widget build(BuildContext context) {
    switch (currentIndex) {
      case 0:
        return const HomeScreen();
      case 1:
        return const SuppliesScreen();
      case 3:
        return const MessagesScreen();
    }
    return const SizedBox.shrink();
  }
}
