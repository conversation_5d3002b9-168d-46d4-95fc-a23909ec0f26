import 'package:connectify_app/generated/assets.dart';
import 'package:connectify_app/src/screens/attendance/controllers/attendance_controller.dart';
import 'package:connectify_app/src/screens/auth/models/user_model.dart';
import 'package:connectify_app/src/screens/exam/controllers/exams_controller.dart';
import 'package:connectify_app/src/screens/exam/models/exam_model.dart';
import 'package:connectify_app/src/screens/exam/view/widgets/exam_tab_bar/exam_selected_screen/results_tab_screen/results_tab_screen.dart';
import 'package:connectify_app/src/screens/home/<USER>/home_screen/widgets/current_activity/current_activity.dart';
import 'package:connectify_app/src/screens/home/<USER>/home_screen/widgets/home_messages/home_messages_widget.dart';
import 'package:connectify_app/src/screens/home/<USER>/home_screen/widgets/required_supplies/required_supplies.dart';
import 'package:connectify_app/src/screens/home/<USER>/main_screen/widgets/main_app_bar.dart';
import 'package:connectify_app/src/screens/messages/controller/messages_controller.dart';
import 'package:connectify_app/src/screens/messages/model/messages_model.dart';
import 'package:connectify_app/src/screens/parent_activity/controllers/teacher_activities_controller.dart';
import 'package:connectify_app/src/shared/extensions/context_extensions.dart';
import 'package:connectify_app/src/shared/extensions/riverpod_extensions.dart';
import 'package:connectify_app/src/shared/widgets/base_container.dart';
import 'package:connectify_app/src/shared/widgets/loading/loading_widget.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:xr_helper/xr_helper.dart';

import 'widgets/today_activities/today_activities_widget.dart';

class HomeScreen extends ConsumerWidget {
  const HomeScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final getTeacherCurrentActivity =
        ref.watch(getTeacherActivitiesByCurrentTimeDataProvider(context));
    final getTeacherTodayActivity =
        ref.watch(getTeacherActivitiesByTodayDataProvider(context));

    final params = (context, DateTime.now().formatDateToString);

    final attendanceCtrl =
        ref.watch(getAttendanceDataForTodayProvider(context));

    final messageController = ref.watch(getHomeMessageData(context));

    final messages = messageController.when(
      data: (data) {
        return data;
      },
      error: (error, stackTrace) {
        return <MessageModel>[];
      },
      loading: () {
        return <MessageModel>[];
      },
    );

    final nurseryMessages =
        messages.where((element) => element.type != 'parent').toList();

    final messagesList = nurseryMessages.take(2).toList();

    final getResultsStudentsFuture =
        ref.watch(getExamsDataByMonthProvider(params));

    final exams = getResultsStudentsFuture.when(
      data: (data) => data,
      loading: () => <ExamModel>[],
      error: (error, stack) => <ExamModel>[],
    );

    return ListView(
      padding: const EdgeInsets.symmetric(vertical: AppSpaces.mediumPadding),
      shrinkWrap: true,
      children: [
        attendanceCtrl.get(
          data: (attendance) {
            final todayDate = DateTime.now().formatDateToString;

            Log.w(
                'attendanceByDate =========== ${attendance.map((e) => e.attendanceDate)}');

            Log.i("todayDate: $todayDate");
            final isStudentAttendance = attendance.isNotEmpty;

            if (!isStudentAttendance) {
              return BaseContainer(
                child: Column(
                  children: [
                    SvgPicture.asset(
                      Assets.svgAbsent,
                      height: 56.h,
                    ),
                    context.mediumGap,
                    Text(
                        context.tr.studentIsAbsentToday(
                            selectedStudent.value?.name ??
                                const UserModel().currentUser.name),
                        style: context.boldTitle)
                  ],
                ),
              );
            }

            return const SizedBox.shrink();
          },
        ),
        context.mediumGap,
        getTeacherCurrentActivity.get(
          data: (teacherActivities) {
            final filteredActivities = teacherActivities.where(
              (element) {
                if (element.date.isNotEmpty && !element.isWeekly) {
                  final date = DateTime.parse(element.date);
                  final now = DateTime.now();
                  return date.day == now.day &&
                      date.month == now.month &&
                      date.year == now.year;
                }

                return true;
              },
            ).toList();

            return CurrentActivity(
              teacherActivities: filteredActivities,
            );
          },
        ),
        context.mediumGap,
        getTeacherTodayActivity.get(
          data: (teacherActivities) {
            final filteredActivities = teacherActivities.where(
              (element) {
                if (element.date.isNotEmpty && !element.isWeekly) {
                  final date = DateTime.parse(element.date);
                  final now = DateTime.now();
                  return date.day == now.day &&
                      date.month == now.month &&
                      date.year == now.year;
                }

                return true;
              },
            ).toList();

            return TodayActivities(
              teacherActivities: filteredActivities.reversed.toList(),
            );
          },
        ),
        context.mediumGap,
        const RequiredSupplies(),
        context.mediumGap,
        HomeMessages(
          messagesList: messagesList,
        ),
        context.mediumGap,
        if (getResultsStudentsFuture.isLoading)
          const LoadingWidget()
        else
          ResultHomeWidget(
            exams: exams,
          ),
      ],
    ).paddingSymmetric(horizontal: AppSpaces.mediumPadding);
  }
}
