import 'package:collection/collection.dart';
import 'package:connectify_app/src/screens/home/<USER>/home_screen/widgets/home_payment/payment_dialog_widget.dart';
import 'package:connectify_app/src/screens/home/<USER>/main_screen/widgets/main_app_bar.dart';
import 'package:connectify_app/src/screens/student/models/student_model.dart';
import 'package:connectify_app/src/shared/extensions/context_extensions.dart';
import 'package:connectify_app/src/shared/widgets/base_container.dart';
import 'package:connectify_app/src/shared/widgets/loading/loading_widget.dart';
import 'package:flutter/material.dart';
import 'package:xr_helper/xr_helper.dart';

class HomePaymentCardWidget extends StatelessWidget {
  const HomePaymentCardWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return ValueListenableBuilder<StudentModel?>(
      valueListenable: selectedStudent,
      builder: (context, studentData, child) {
        return StatefulBuilder(builder: (context, setState) {
          final student = selectedStudent.value;
          // rebuild each sec if student is null
          if (student == null) {
            Future.delayed(const Duration(seconds: 1), () {
              setState(() {});
            });
          }

          if (student == null) {
            return const LoadingWidget();
          }

          // Check payment status - use current month for simplicity
          final now = DateTime.now();
          final nextPaymentDate = DateTime(now.year, now.month, 1);

          // Check for current period subscription
          final currentPeriodSubscription =
              student.subscriptions.lastWhereOrNull(
            (element) {
              final paymentDate = element.date.formatStringToDateTime;
              return paymentDate.year == nextPaymentDate.year &&
                  paymentDate.month == nextPaymentDate.month &&
                  element.isPaid;
            },
          );

          // Check for pending payment request
          final pendingPaymentRequest = currentPeriodSubscription == null
              ? student.subscriptions.lastWhereOrNull(
                  (element) {
                    return !element.isPaid &&
                        !element.isApproved &&
                        element.paymentScreenshot != null;
                  },
                )
              : null;

          // If already paid for current period, don't show card
          if (currentPeriodSubscription != null) {
            return const SizedBox.shrink();
          }

          final amount = student.fees ?? 0;
          return BaseContainer(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Icon(
                      Icons.payment,
                      color: pendingPaymentRequest != null
                          ? Colors.blue
                          : Colors.orange,
                      size: 24,
                    ),
                    context.smallGap,
                    Expanded(
                      child: Text(
                        pendingPaymentRequest != null
                            ? context.tr.paymentPending
                            : context.tr.paymentDue,
                        style: context.boldTitle.copyWith(
                          color: pendingPaymentRequest != null
                              ? Colors.blue
                              : Colors.orange,
                        ),
                      ),
                    ),
                    Text(
                      '\$${amount.toString()}',
                      style: context.priceTitle,
                    ),
                  ],
                ),
                context.smallGap,
                Text(
                  student.name,
                  style: context.blueHint.copyWith(fontWeight: FontWeight.bold),
                ),
                context.xSmallGap,
                Text(
                  pendingPaymentRequest != null
                      ? context.tr.paymentPendingApproval
                      : '${context.tr.due}: ${nextPaymentDate.formatDateToString}',
                  style: context.hint.copyWith(
                    fontSize: 12,
                    color: pendingPaymentRequest != null
                        ? Colors.blue
                        : Colors.orange,
                  ),
                ),
                if (pendingPaymentRequest?.paymentMethod != null) ...[
                  context.xSmallGap,
                  Text(
                    '${context.tr.paymentMethod}: ${pendingPaymentRequest!.paymentMethod}',
                    style: context.hint.copyWith(
                      fontSize: 11,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ],
                context.mediumGap,
                if (pendingPaymentRequest == null)
                  SizedBox(
                    width: double.infinity,
                    child: ElevatedButton(
                      onPressed: () {
                        showDialog(
                          context: context,
                          builder: (context) => PaymentDialogWidget(
                            student: student,
                            amount: amount,
                            nextPaymentDate: nextPaymentDate,
                          ),
                        );
                      },
                      style: ElevatedButton.styleFrom(
                        backgroundColor: ColorManager.buttonColor,
                        foregroundColor: Colors.white,
                      ),
                      child: Text(context.tr.payNow),
                    ),
                  ),
              ],
            ),
          );
        });
      },
    );
  }
}
