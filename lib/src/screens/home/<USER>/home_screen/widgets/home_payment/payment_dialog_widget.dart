import 'package:connectify_app/src/screens/home/<USER>/main_screen.dart';
import 'package:connectify_app/src/screens/nursery/models/nursery_model_helper.dart';
import 'package:connectify_app/src/screens/student/controllers/student_controller.dart';
import 'package:connectify_app/src/screens/student/models/student_model.dart';
import 'package:connectify_app/src/shared/extensions/context_extensions.dart';
import 'package:connectify_app/src/shared/providers/network_api_service_provider.dart';
import 'package:connectify_app/src/shared/services/media/controller/media_controller.dart';
import 'package:connectify_app/src/shared/shared_models/base_media_model.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:xr_helper/xr_helper.dart';

class PaymentDialogWidget extends HookConsumerWidget {
  final StudentModel student;
  final num amount;
  final DateTime nextPaymentDate;

  const PaymentDialogWidget({
    super.key,
    required this.student,
    required this.amount,
    required this.nextPaymentDate,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final selectedPaymentMethod = useState<String?>(null);
    final isLoading = useState(false);
    final uploadedScreenshot = useState<BaseMediaModel?>(null);
    final isUploadingScreenshot = useState(false);

    final mediaController = ref.watch(mediaPickerControllerProvider);
    final networkApiServices = ref.watch(networkServiceProvider);

    // Get available payment methods from nursery
    final currentNursery = NurseryModelHelper.currentNursery();
    final nurseryPaymentMethods = currentNursery?.paymentMethods;
    final availablePaymentMethods = nurseryPaymentMethods?.availablePaymentMethods ?? [];

    return Dialog(
      child: Padding(
        padding: const EdgeInsets.all(24.0),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header
            Row(
              children: [
                Expanded(
                  child: Text(
                    context.tr.payNow,
                    style: context.boldTitle,
                  ),
                ),
                IconButton(
                  onPressed: () => Navigator.of(context).pop(),
                  icon: const Icon(Icons.close),
                ),
              ],
            ),

            context.largeGap,

            // Content
            _buildDialogContent(context, ref, selectedPaymentMethod, availablePaymentMethods, uploadedScreenshot, isUploadingScreenshot, mediaController, networkApiServices, nurseryPaymentMethods),

            context.largeGap,

            // Buttons
            Row(
              children: [
                Expanded(
                  child: TextButton(
                    onPressed: () => Navigator.of(context).pop(),
                    child: Text(context.tr.cancel),
                  ),
                ),
                context.mediumGap,
                Expanded(
                  child: ElevatedButton(
                    onPressed: isLoading.value ? null : () async {
                      await _handlePayment(context, ref, selectedPaymentMethod, uploadedScreenshot, isLoading);
                    },
                    style: ElevatedButton.styleFrom(
                      backgroundColor: ColorManager.buttonColor,
                      foregroundColor: Colors.white,
                    ),
                    child: isLoading.value
                        ? const SizedBox(
                            height: 20,
                            width: 20,
                            child: CircularProgressIndicator(strokeWidth: 2),
                          )
                        : Text(context.tr.payNow),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDialogContent(
    BuildContext context,
    WidgetRef ref,
    ValueNotifier<String?> selectedPaymentMethod,
    List<String> availablePaymentMethods,
    ValueNotifier<BaseMediaModel?> uploadedScreenshot,
    ValueNotifier<bool> isUploadingScreenshot,
    MediaPickerController mediaController,
    BaseApiServices networkApiServices,
    dynamic nurseryPaymentMethods,
  ) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Student info
        Text(
          '${context.tr.student}: ${student.name}',
          style: context.boldTitle,
        ),
        context.smallGap,
        Text(
          '${context.tr.amount}: \$${amount.toString()}',
          style: context.title,
        ),
        context.smallGap,
        Text(
          '${context.tr.dueDate}: ${nextPaymentDate.formatDateToString}',
          style: context.hint,
        ),

        context.largeGap,

        // Payment method dropdown
        Text(
          context.tr.paymentMethod,
          style: context.boldTitle,
        ),
        context.smallGap,
        BaseDropDown(
          label: context.tr.selectPaymentMethod,
          onChanged: (value) {
            selectedPaymentMethod.value = value;
          },
          data: availablePaymentMethods,
          asString: (method) => method,
          selectedValue: selectedPaymentMethod.value,
        ),

        context.largeGap,

        // Screenshot upload section
        Text(
          context.tr.uploadPaymentScreenshot,
          style: context.boldTitle,
        ),
        context.smallGap,
        Text(
          context.tr.pleaseUploadPaymentScreenshot,
          style: context.hint,
        ),
        context.mediumGap,

        // Upload button
        SizedBox(
          width: double.infinity,
          child: ElevatedButton.icon(
            onPressed: isUploadingScreenshot.value ? null : () async {
              await _uploadScreenshot(context, ref, uploadedScreenshot, isUploadingScreenshot, mediaController, networkApiServices);
            },
            icon: isUploadingScreenshot.value
                ? const SizedBox(
                    height: 16,
                    width: 16,
                    child: CircularProgressIndicator(strokeWidth: 2),
                  )
                : const Icon(Icons.upload),
            label: Text(
              uploadedScreenshot.value != null
                  ? context.tr.screenshotUploaded
                  : context.tr.uploadScreenshot,
            ),
            style: ElevatedButton.styleFrom(
              backgroundColor: uploadedScreenshot.value != null ? Colors.green : Colors.grey,
              foregroundColor: Colors.white,
            ),
          ),
        ),

        if (uploadedScreenshot.value != null) ...[
          context.smallGap,
          Container(
            height: 100,
            width: double.infinity,
            decoration: BoxDecoration(
              border: Border.all(color: Colors.grey),
              borderRadius: BorderRadius.circular(8),
            ),
            child: ClipRRect(
              borderRadius: BorderRadius.circular(8),
              child: Image.network(
                uploadedScreenshot.value!.url ?? '',
                fit: BoxFit.cover,
                errorBuilder: (context, error, stackTrace) {
                  return const Center(
                    child: Icon(Icons.image_not_supported),
                  );
                },
              ),
            ),
          ),
        ],

        // Pay button for direct payment methods
        if (selectedPaymentMethod.value != null && uploadedScreenshot.value != null) ...[
          context.largeGap,
          _buildPayButton(context, selectedPaymentMethod.value!, nurseryPaymentMethods),
        ],
      ],
    );
  }

  Widget _buildPayButton(BuildContext context, String paymentMethod, dynamic nurseryPaymentMethods) {
    final paymentValue = nurseryPaymentMethods?.getPaymentMethodValue(paymentMethod);

    if (paymentValue == null || paymentValue.isEmpty) {
      return const SizedBox.shrink();
    }

    // Check if InstaPay is a link
    final isInstapayLink = paymentMethod.toLowerCase() == 'instapay' &&
        (paymentValue.contains('http') || paymentValue.contains('https'));

    return Container(
      decoration: BoxDecoration(
        color: ColorManager.primaryColor.withValues(alpha: 0.2),
        borderRadius: BorderRadius.circular(8),
      ),
      padding: const EdgeInsets.all(12),
      child: Column(
        children: [
          Row(
            children: [
              const Icon(Icons.payment, color: Colors.blue),
              context.smallGap,
              Expanded(
                child: Text(
                  paymentMethod,
                  style: context.boldTitle.copyWith(fontSize: 14),
                ),
              ),
            ],
          ),
          context.smallGap,
          SizedBox(
            width: double.infinity,
            child: ElevatedButton.icon(
              onPressed: () => _handleDirectPayment(context, paymentMethod, paymentValue, isInstapayLink),
              icon: Icon(isInstapayLink ? Icons.link : Icons.phone),
              label: Text(context.tr.payNow),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.blue,
                foregroundColor: Colors.white,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Future<void> _handleDirectPayment(BuildContext context, String paymentMethod, String paymentValue, bool isInstapayLink) async {
    try {
      if (isInstapayLink) {
        // Open InstaPay link
        final uri = Uri.parse(paymentValue);
        if (await canLaunchUrl(uri)) {
          await launchUrl(uri, mode: LaunchMode.externalApplication);
        } else {
          if (context.mounted) {
            context.showBarMessage('Could not open payment link', isError: true);
          }
        }
      } else {
        // Generate USSD code for mobile wallets
        String ussdCode = '';

        switch (paymentMethod.toLowerCase()) {
          case 'vodafone cash':
            ussdCode = '*9*7*$paymentValue*${amount.toString()}#';
            break;
          case 'etisalat cash':
            ussdCode = '*777*1#';
            break;
          case 'orange cash':
            ussdCode = '#7115#';
            break;
          case 'we cash':
            ussdCode = '*551*$paymentValue*${amount.toString()}#';
            break;
        }

        if (ussdCode.isNotEmpty) {
          final uri = Uri.parse('tel:$ussdCode');
          if (await canLaunchUrl(uri)) {
            await launchUrl(uri);
          } else {
            if (context.mounted) {
              context.showBarMessage('Could not open dialer', isError: true);
            }
          }
        }
      }
    } catch (e) {
      if (context.mounted) {
        context.showBarMessage('Error: $e', isError: true);
      }
    }
  }

  Future<void> _uploadScreenshot(
    BuildContext context,
    WidgetRef ref,
    ValueNotifier<BaseMediaModel?> uploadedScreenshot,
    ValueNotifier<bool> isUploadingScreenshot,
    MediaPickerController mediaController,
    BaseApiServices networkApiServices,
  ) async {
    isUploadingScreenshot.value = true;

    try {
      final result = await mediaController.pickFile(allowMultiple: false);

      if (result != null && result.files.isNotEmpty) {
        final file = result.files.first;

        if (file.path == null) {
          if (context.mounted) {
            context.showBarMessage('File path is null', isError: true);
          }
          return;
        }

        final uploadedMedia = await networkApiServices.uploadFiles(
          filePaths: [file.path!],
        );

        if (uploadedMedia != null && uploadedMedia.isNotEmpty) {
          final uploadedFile = uploadedMedia.first;

          uploadedScreenshot.value = BaseMediaModel(
            id: uploadedFile['id'],
            url: uploadedFile['url'],
          );

          if (context.mounted) {
            context.showBarMessage(context.tr.screenshotUploaded);
          }
        }
      }
    } catch (e) {
      if (context.mounted) {
        context.showBarMessage(e.toString(), isError: true);
      }
    } finally {
      isUploadingScreenshot.value = false;
    }
  }

  Future<void> _handlePayment(BuildContext context, WidgetRef ref, ValueNotifier<String?> selectedPaymentMethod, ValueNotifier<BaseMediaModel?> uploadedScreenshot, ValueNotifier<bool> isLoading) async {
    if (selectedPaymentMethod.value == null || uploadedScreenshot.value == null) {
      if (context.mounted) {
        context.showBarMessage(context.tr.pleaseSelectPaymentMethodAndUploadScreenshot, isError: true);
      }
      return;
    }

    isLoading.value = true;

    try {
      final studentCtrl = ref.read(studentChangeNotifierProvider(context));

      // Create new subscription with payment details and screenshot
      final newSubscription = SubscriptionModel(
        amount: amount,
        date: nextPaymentDate.formatDateToString,
        isPaid: false,
        isApproved: false,
        paymentMethod: selectedPaymentMethod.value,
        paymentScreenshot: uploadedScreenshot.value,
      );

      final updatedSubscriptions = [
        ...student.subscriptions,
        newSubscription,
      ];

      await studentCtrl.paySubscription(
        studentId: student.id!,
        subscriptions: updatedSubscriptions,
        navigateWidget: const MainScreen(),
      );

    } catch (e) {
      if (context.mounted) {
        context.showBarMessage(e.toString(), isError: true);
      }
    } finally {
      isLoading.value = false;
    }
  }
}
