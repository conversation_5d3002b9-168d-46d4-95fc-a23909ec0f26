import 'package:connectify_app/src/screens/home/<USER>/main_screen.dart';
import 'package:connectify_app/src/screens/student/controllers/student_controller.dart';
import 'package:connectify_app/src/screens/student/models/student_model.dart';
import 'package:connectify_app/src/shared/consts/app_constants.dart';
import 'package:connectify_app/src/shared/extensions/context_extensions.dart';
import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:xr_helper/xr_helper.dart';

class PaymentDialogWidget extends HookConsumerWidget {
  final StudentModel student;
  final num amount;
  final DateTime nextPaymentDate;

  const PaymentDialogWidget({
    super.key,
    required this.student,
    required this.amount,
    required this.nextPaymentDate,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final selectedPaymentMethod = useState<String?>(null);
    final isLoading = useState(false);

    final paymentMethods = [
      AppConsts.instapay,
      AppConsts.vodafoneCash,
      AppConsts.etisalatCash,
      AppConsts.weCash,
      AppConsts.orangeCash,
    ];

    return Dialog(
      child: Padding(
        padding: const EdgeInsets.all(24.0),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header
            Row(
              children: [
                Expanded(
                  child: Text(
                    context.tr.payNow,
                    style: context.boldTitle,
                  ),
                ),
                IconButton(
                  onPressed: () => Navigator.of(context).pop(),
                  icon: const Icon(Icons.close),
                ),
              ],
            ),

            context.largeGap,

            // Content
            _buildDialogContent(context, selectedPaymentMethod, paymentMethods),

            context.largeGap,

            // Buttons
            Row(
              children: [
                Expanded(
                  child: TextButton(
                    onPressed: () => Navigator.of(context).pop(),
                    child: Text(context.tr.cancel),
                  ),
                ),
                context.mediumGap,
                Expanded(
                  child: ElevatedButton(
                    onPressed: isLoading.value ? null : () async {
                      await _handlePayment(context, ref, selectedPaymentMethod, isLoading);
                    },
                    style: ElevatedButton.styleFrom(
                      backgroundColor: ColorManager.buttonColor,
                      foregroundColor: Colors.white,
                    ),
                    child: isLoading.value
                        ? const SizedBox(
                            height: 20,
                            width: 20,
                            child: CircularProgressIndicator(strokeWidth: 2),
                          )
                        : Text(context.tr.payNow),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDialogContent(BuildContext context, ValueNotifier<String?> selectedPaymentMethod, List<String> paymentMethods) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Student info
        Text(
          '${context.tr.student}: ${student.name}',
          style: context.boldTitle,
        ),
        context.smallGap,
        Text(
          '${context.tr.amount}: \$${amount.toString()}',
          style: context.title,
        ),
        context.smallGap,
        Text(
          '${context.tr.dueDate}: ${nextPaymentDate.formatDateToString}',
          style: context.hint,
        ),

        context.largeGap,

        // Payment method dropdown
        Text(
          context.tr.paymentMethod,
          style: context.boldTitle,
        ),
        context.smallGap,
        BaseDropDown(
          label: context.tr.selectPaymentMethod,
          onChanged: (value) {
            selectedPaymentMethod.value = value;
          },
          data: paymentMethods,
          asString: (method) => method,
          selectedValue: selectedPaymentMethod.value,
        ),

        context.largeGap,

        // Instructions
        Text(
          context.tr.uploadPaymentScreenshot,
          style: context.boldTitle,
        ),
        context.smallGap,
        Text(
          'Please upload a screenshot of your payment confirmation.',
          style: context.hint,
        ),
      ],
    );
  }

  Future<void> _handlePayment(BuildContext context, WidgetRef ref, ValueNotifier<String?> selectedPaymentMethod, ValueNotifier<bool> isLoading) async {
    if (selectedPaymentMethod.value == null) {
      if (context.mounted) {
        context.showBarMessage(context.tr.selectPaymentMethod, isError: true);
      }
      return;
    }

    isLoading.value = true;

    try {
      final studentCtrl = ref.read(studentChangeNotifierProvider(context));

      // Create new subscription with payment details
      final newSubscription = SubscriptionModel(
        amount: amount,
        date: nextPaymentDate.formatDateToString,
        isPaid: false,
        isApproved: false,
        paymentMethod: selectedPaymentMethod.value,
      );

      final updatedSubscriptions = [
        ...student.subscriptions,
        newSubscription,
      ];

      await studentCtrl.paySubscription(
        studentId: student.id!,
        subscriptions: updatedSubscriptions,
        navigateWidget: const MainScreen(),
      );

    } catch (e) {
      if (context.mounted) {
        context.showBarMessage(e.toString(), isError: true);
      }
    } finally {
      isLoading.value = false;
    }
  }
}
