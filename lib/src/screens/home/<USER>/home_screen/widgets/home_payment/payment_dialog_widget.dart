import 'package:connectify_app/src/screens/home/<USER>/main_screen.dart';
import 'package:connectify_app/src/screens/student/controllers/student_controller.dart';
import 'package:connectify_app/src/screens/student/models/student_model.dart';
import 'package:connectify_app/src/shared/consts/app_constants.dart';
import 'package:connectify_app/src/shared/extensions/context_extensions.dart';
import 'package:connectify_app/src/shared/providers/network_api_service_provider.dart';
import 'package:connectify_app/src/shared/services/media/controller/media_controller.dart';
import 'package:connectify_app/src/shared/shared_models/base_media_model.dart';
import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:xr_helper/xr_helper.dart';

class PaymentDialogWidget extends HookConsumerWidget {
  final StudentModel student;
  final num amount;
  final DateTime nextPaymentDate;

  const PaymentDialogWidget({
    super.key,
    required this.student,
    required this.amount,
    required this.nextPaymentDate,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final selectedPaymentMethod = useState<String?>(null);
    final isLoading = useState(false);
    final uploadedScreenshot = useState<BaseMediaModel?>(null);
    final isUploadingScreenshot = useState(false);

    final mediaController = ref.watch(mediaPickerControllerProvider);
    final networkApiServices = ref.watch(networkServiceProvider);

    final paymentMethods = [
      AppConsts.instapay,
      AppConsts.vodafoneCash,
      AppConsts.etisalatCash,
      AppConsts.weCash,
      AppConsts.orangeCash,
    ];

    return Dialog(
      child: Padding(
        padding: const EdgeInsets.all(24.0),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header
            Row(
              children: [
                Expanded(
                  child: Text(
                    context.tr.payNow,
                    style: context.boldTitle,
                  ),
                ),
                IconButton(
                  onPressed: () => Navigator.of(context).pop(),
                  icon: const Icon(Icons.close),
                ),
              ],
            ),

            context.largeGap,

            // Content
            _buildDialogContent(context, ref, selectedPaymentMethod, paymentMethods, uploadedScreenshot, isUploadingScreenshot, mediaController, networkApiServices),

            context.largeGap,

            // Buttons
            Row(
              children: [
                Expanded(
                  child: TextButton(
                    onPressed: () => Navigator.of(context).pop(),
                    child: Text(context.tr.cancel),
                  ),
                ),
                context.mediumGap,
                Expanded(
                  child: ElevatedButton(
                    onPressed: isLoading.value ? null : () async {
                      await _handlePayment(context, ref, selectedPaymentMethod, uploadedScreenshot, isLoading);
                    },
                    style: ElevatedButton.styleFrom(
                      backgroundColor: ColorManager.buttonColor,
                      foregroundColor: Colors.white,
                    ),
                    child: isLoading.value
                        ? const SizedBox(
                            height: 20,
                            width: 20,
                            child: CircularProgressIndicator(strokeWidth: 2),
                          )
                        : Text(context.tr.payNow),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDialogContent(
    BuildContext context,
    WidgetRef ref,
    ValueNotifier<String?> selectedPaymentMethod,
    List<String> paymentMethods,
    ValueNotifier<BaseMediaModel?> uploadedScreenshot,
    ValueNotifier<bool> isUploadingScreenshot,
    MediaPickerController mediaController,
    BaseApiServices networkApiServices,
  ) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Student info
        Text(
          '${context.tr.student}: ${student.name}',
          style: context.boldTitle,
        ),
        context.smallGap,
        Text(
          '${context.tr.amount}: \$${amount.toString()}',
          style: context.title,
        ),
        context.smallGap,
        Text(
          '${context.tr.dueDate}: ${nextPaymentDate.formatDateToString}',
          style: context.hint,
        ),

        context.largeGap,

        // Payment method dropdown
        Text(
          context.tr.paymentMethod,
          style: context.boldTitle,
        ),
        context.smallGap,
        BaseDropDown(
          label: context.tr.selectPaymentMethod,
          onChanged: (value) {
            selectedPaymentMethod.value = value;
          },
          data: paymentMethods,
          asString: (method) => method,
          selectedValue: selectedPaymentMethod.value,
        ),

        context.largeGap,

        // Screenshot upload section
        Text(
          context.tr.uploadPaymentScreenshot,
          style: context.boldTitle,
        ),
        context.smallGap,
        Text(
          context.tr.pleaseUploadPaymentScreenshot,
          style: context.hint,
        ),
        context.mediumGap,

        // Upload button
        SizedBox(
          width: double.infinity,
          child: ElevatedButton.icon(
            onPressed: isUploadingScreenshot.value ? null : () async {
              await _uploadScreenshot(context, ref, uploadedScreenshot, isUploadingScreenshot, mediaController, networkApiServices);
            },
            icon: isUploadingScreenshot.value
                ? const SizedBox(
                    height: 16,
                    width: 16,
                    child: CircularProgressIndicator(strokeWidth: 2),
                  )
                : const Icon(Icons.upload),
            label: Text(
              uploadedScreenshot.value != null
                  ? context.tr.screenshotUploaded
                  : context.tr.uploadScreenshot,
            ),
            style: ElevatedButton.styleFrom(
              backgroundColor: uploadedScreenshot.value != null ? Colors.green : Colors.grey,
              foregroundColor: Colors.white,
            ),
          ),
        ),

        if (uploadedScreenshot.value != null) ...[
          context.smallGap,
          Container(
            height: 100,
            width: double.infinity,
            decoration: BoxDecoration(
              border: Border.all(color: Colors.grey),
              borderRadius: BorderRadius.circular(8),
            ),
            child: ClipRRect(
              borderRadius: BorderRadius.circular(8),
              child: Image.network(
                uploadedScreenshot.value!.url ?? '',
                fit: BoxFit.cover,
                errorBuilder: (context, error, stackTrace) {
                  return const Center(
                    child: Icon(Icons.image_not_supported),
                  );
                },
              ),
            ),
          ),
        ],
      ],
    );
  }

  Future<void> _uploadScreenshot(
    BuildContext context,
    WidgetRef ref,
    ValueNotifier<BaseMediaModel?> uploadedScreenshot,
    ValueNotifier<bool> isUploadingScreenshot,
    MediaPickerController mediaController,
    BaseApiServices networkApiServices,
  ) async {
    isUploadingScreenshot.value = true;

    try {
      final result = await mediaController.pickFile(allowMultiple: false);

      if (result != null && result.files.isNotEmpty) {
        final file = result.files.first;

        if (file.path == null) {
          if (context.mounted) {
            context.showBarMessage('File path is null', isError: true);
          }
          return;
        }

        final uploadedMedia = await networkApiServices.uploadFiles(
          filePaths: [file.path!],
        );

        if (uploadedMedia != null && uploadedMedia.isNotEmpty) {
          final uploadedFile = uploadedMedia.first;

          uploadedScreenshot.value = BaseMediaModel(
            id: uploadedFile['id'],
            url: uploadedFile['url'],
          );

          if (context.mounted) {
            context.showBarMessage(context.tr.screenshotUploaded);
          }
        }
      }
    } catch (e) {
      if (context.mounted) {
        context.showBarMessage(e.toString(), isError: true);
      }
    } finally {
      isUploadingScreenshot.value = false;
    }
  }

  Future<void> _handlePayment(BuildContext context, WidgetRef ref, ValueNotifier<String?> selectedPaymentMethod, ValueNotifier<BaseMediaModel?> uploadedScreenshot, ValueNotifier<bool> isLoading) async {
    if (selectedPaymentMethod.value == null || uploadedScreenshot.value == null) {
      if (context.mounted) {
        context.showBarMessage(context.tr.pleaseSelectPaymentMethodAndUploadScreenshot, isError: true);
      }
      return;
    }

    isLoading.value = true;

    try {
      final studentCtrl = ref.read(studentChangeNotifierProvider(context));

      // Create new subscription with payment details and screenshot
      final newSubscription = SubscriptionModel(
        amount: amount,
        date: nextPaymentDate.formatDateToString,
        isPaid: false,
        isApproved: false,
        paymentMethod: selectedPaymentMethod.value,
        paymentScreenshot: uploadedScreenshot.value,
      );

      final updatedSubscriptions = [
        ...student.subscriptions,
        newSubscription,
      ];

      await studentCtrl.paySubscription(
        studentId: student.id!,
        subscriptions: updatedSubscriptions,
        navigateWidget: const MainScreen(),
      );

    } catch (e) {
      if (context.mounted) {
        context.showBarMessage(e.toString(), isError: true);
      }
    } finally {
      isLoading.value = false;
    }
  }
}
