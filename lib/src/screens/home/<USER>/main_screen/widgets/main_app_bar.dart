import 'package:collection/collection.dart';
import 'package:connectify_app/generated/assets.dart';
import 'package:connectify_app/src/screens/notification/controller/notification_controller.dart';
import 'package:connectify_app/src/screens/notification/view/notifications_screen.dart';
import 'package:connectify_app/src/screens/nursery/models/nursery_model_helper.dart';
import 'package:connectify_app/src/screens/settings/view/settings_screen/settings_screen.dart';
import 'package:connectify_app/src/shared/services/notifications/local_notifications_service.dart';
import 'package:connectify_app/src/shared/widgets/shared_widgets.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:restart_app/restart_app.dart';
import 'package:xr_helper/xr_helper.dart';

import '../../../../../shared/widgets/icon_widget/icon_widget.dart';
import '../../../../auth/models/user_model.dart';
import '../../../../student/controllers/student_controller.dart';
import '../../../../student/models/student_model.dart';

ValueNotifier<int?> notificationCountValue = ValueNotifier<int?>(null);

ValueNotifier<StudentModel?> selectedStudent =
    ValueNotifier<StudentModel?>(null);

ValueNotifier<List<StudentModel>> studentsNotifier =
    ValueNotifier<List<StudentModel>>(<StudentModel>[]);

class MainAppBar extends ConsumerWidget implements PreferredSizeWidget {
  final String title;
  final bool isHome;
  final bool isBackButton;
  final String iconPath;
  final Widget? titleWidget;

  const MainAppBar(
      {super.key,
      this.isBackButton = false,
      this.isHome = false,
      required this.title,
      this.titleWidget,
      this.iconPath = Assets.svgNotification});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final getStudents = ref.watch(getStudentsByIdsProvider(context));

    return ValueListenableBuilder(
        valueListenable: studentsNotifier,
        builder: (context, students, child) {
          return Consumer(
            builder: (context, ref, child) {
              final localNotificationCount = GetStorageService.getLocalData(
                key: LocalKeys.notificationCount,
              );

              final notificationCount =
                  ref.watch(getNotificationData(context)).when(
                data: (data) {
                  final onlineNotificationCount = data.length;
                  final newNotifications = onlineNotificationCount -
                      (int.tryParse(localNotificationCount.toString()) ?? 0);
                  notificationCountValue.value =
                      newNotifications > 0 ? newNotifications : 0;
                  return notificationCountValue.value!;
                },
                error: (error, stackTrace) {
                  return 0;
                },
                loading: () {
                  return 0;
                },
              );

              if (studentsNotifier.value.isEmpty) {
                WidgetsBinding.instance.addPostFrameCallback((_) {
                  studentsNotifier.value = getStudents.when(
                    loading: () => <StudentModel>[],
                    error: (e, s) => <StudentModel>[],
                    data: (students) {
                      if (students.isNotEmpty) {
                        final selectedLocalStudent =
                            GetStorageService.getLocalData(
                          key: LocalKeys.studentId,
                        );

                        if (selectedStudent.value == null) {
                          selectedStudent.value = students.firstWhereOrNull(
                                (element) => element.id == selectedLocalStudent,
                              ) ??
                              students.firstOrNull;
                        }

                        // Schedule payment reminders for all students
                        _schedulePaymentReminders(students);
                      }
                      return students;
                    },
                  );
                });
              }

              return AppBar(
                automaticallyImplyLeading: isBackButton ? true : false,
                toolbarHeight: 80.h,
                shape: const RoundedRectangleBorder(
                    borderRadius: BorderRadius.only(
                  bottomRight: Radius.circular(AppRadius.appBarRadius),
                  bottomLeft: Radius.circular(AppRadius.appBarRadius),
                )),
                title: Row(
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          title,
                          style: context.whiteHeadLine
                              .copyWith(fontSize: isHome ? null : 16),
                        ).paddingOnly(top: AppSpaces.xSmallPadding + 3),
                        context.smallGap,
                        if (isHome)
                          Row(
                            children: [
                              Row(
                                children: [
                                  if (NurseryModelHelper.currentNursery()
                                          ?.showNurseryLogoInParentApp ==
                                      true)
                                    BaseCachedImage(
                                      NurseryModelHelper.currentNursery()
                                              ?.image
                                              ?.url ??
                                          '',
                                      width: 40,
                                      height: 40,
                                      radius: AppRadius.baseRadius,
                                      errorWidget:
                                          Image.asset(Assets.imagesLogo),
                                    ).paddingOnly(
                                        right: context.isEng
                                            ? AppSpaces.smallPadding
                                            : 0,
                                        left: context.isEng
                                            ? 0
                                            : AppSpaces.smallPadding),
                                  if (students.isEmpty || students.length == 1)
                                    Text(
                                      selectedStudent.value?.name ??
                                          const UserModel()
                                              .currentUser
                                              .nameWithoutNumbersAndWithoutAnySpacedInLast,
                                      style: context.whiteLabelLarge,
                                    ),
                                ],
                              ),
                              if (students.isEmpty) context.largeGap,
                              if (isHome && students.length > 1)
                                SizedBox(
                                  width: context.width * 0.55,
                                  child: Builder(
                                    builder: (context) {
                                      return ValueListenableBuilder(
                                        valueListenable: selectedStudent,
                                        builder: (context, value, child) {
                                          return BaseDropDown(
                                            isWhiteText: true,
                                            onChanged: (value) {
                                              selectedStudent.value = value;

                                              GetStorageService.setLocalData(
                                                key: LocalKeys.studentId,
                                                value:
                                                    selectedStudent.value?.id,
                                              );

                                              GetStorageService.setLocalData(
                                                key: LocalKeys.classId,
                                                value: selectedStudent
                                                    .value?.classModel?.id,
                                              );

                                              if (!kDebugMode) {
                                                Restart.restartApp();
                                              }
                                            },
                                            data: students,
                                            asString: (e) =>
                                                (e as StudentModel?)?.name ??
                                                '-',
                                            selectedValue:
                                                selectedStudent.value,
                                          );
                                        },
                                      );
                                    },
                                  ),
                                  // getStudents.get(
                                  //   loading: () => const LoadingWidget(
                                  //     loadingType: LoadingType.linear,
                                  //   ),
                                  //   data: (students) {
                                  //
                                  //   },
                                  // ),
                                ),
                            ],
                          )
                      ],
                    ),
                  ],
                ),
                actions: [
                  if (isHome)
                    Row(
                      children: [
                        InkWell(
                          onTap: () {
                            if (notificationCount != 0) {
                              GetStorageService.setLocalData(
                                key: LocalKeys.notificationCount,
                                value:
                                    ref.read(getNotificationData(context)).when(
                                          data: (data) => data.length,
                                          error: (error, stackTrace) => 0,
                                          loading: () => 0,
                                        ),
                              );
                              notificationCountValue.value = 0;
                            }
                            context.to(const NotificationScreen());
                          },
                          child: Stack(
                            alignment: Alignment.topRight,
                            clipBehavior: Clip.none,
                            children: [
                              const IconWidget(icon: Assets.svgNotification),
                              if (notificationCount > 0)
                                Positioned(
                                  top: -5,
                                  right: -5,
                                  child: CircleAvatar(
                                    radius: 7,
                                    backgroundColor: Colors.red,
                                    child: Text(
                                      notificationCount.toString(),
                                      style: context.hint.copyWith(
                                          fontSize: 10,
                                          color: ColorManager.white),
                                    ),
                                  ),
                                )
                            ],
                          ),
                        ),
                        context.mediumGap,
                        InkWell(
                          onTap: () => context.to(const SettingsScreen()),
                          child:
                              const IconWidget(icon: Assets.svgParentSettings),
                        ),
                      ],
                    ).paddingOnly(right: 10, left: 10),
                ],
              );
            },
          );
        });
  }

  @override
  Size get preferredSize => Size.fromHeight(isHome ? 130 : 60);

  // Helper method to schedule payment reminders
  static void _schedulePaymentReminders(List<StudentModel> students) {
    // Run in background to avoid blocking UI
    Future.microtask(() async {
      try {
        await LocalNotificationsService
            .scheduleSubscriptionRemindersForStudents(
          students: students,
        );
        Log.w('Payment reminders scheduled for ${students.length} students');
      } catch (e) {
        Log.e('Error scheduling payment reminders: $e');
      }
    });
  }
}

// import 'package:connectify_app/generated/assets.dart';
// import 'package:connectify_app/src/screens/auth/models/user_model.dart';
// import 'package:connectify_app/src/screens/notification/controller/notification_controller.dart';
// import 'package:connectify_app/src/screens/notification/view/notifications_screen.dart';
// import 'package:connectify_app/src/screens/settings/view/settings_screen/settings_screen.dart';
// import 'package:flutter/material.dart';
// import 'package:flutter_screenutil/flutter_screenutil.dart';
// import 'package:hooks_riverpod/hooks_riverpod.dart';
// import 'package:xr_helper/xr_helper.dart';
//
// import '../../../../../shared/widgets/icon_widget/icon_widget.dart';
//
// ValueNotifier<int?> notificationCountValue = ValueNotifier<int?>(null);
//
// class MainAppBar extends ConsumerWidget implements PreferredSizeWidget {
//   final String title;
//   final bool isHome;
//   final bool isBackButton;
//   final String iconPath;
//   final Widget? titleWidget;
//
//   const MainAppBar(
//       {super.key,
//       this.isBackButton = false,
//       this.isHome = false,
//       required this.title,
//       this.titleWidget,
//       this.iconPath = Assets.svgNotification});
//
//   @override
//   Widget build(BuildContext context, WidgetRef ref) {
//     return Consumer(
//       builder: (context, ref, child) {
//         final localNotificationCount = GetStorageService.getLocalData(
//           key: LocalKeys.notificationCount,
//         );
//
//         final notificationCount = iconPath != ''
//             ? (notificationCountValue.value == null
//                 ? ref.watch(getNotificationData(context)).when(
//                     data: (data) {
//                       notificationCountValue.value = data.length;
//
//                       if (data.isNotEmpty &&
//                           data.length == localNotificationCount) {
//                         return 0;
//                       }
//
//                       return data.length -
//                           (int.tryParse(localNotificationCount.toString()) ??
//                               0);
//                     },
//                     error: (error, stackTrace) {
//                       return '';
//                     },
//                     loading: () {
//                       return '';
//                     },
//                   )
//                 : (notificationCountValue.value == localNotificationCount
//                     ? 0
//                     : notificationCountValue.value! -
//                         (int.tryParse(localNotificationCount.toString()) ?? 0)))
//             : 0;
//
//         return AppBar(
//           automaticallyImplyLeading: isBackButton ? true : false,
//           toolbarHeight: 80.h,
//           shape: const RoundedRectangleBorder(
//               borderRadius: BorderRadius.only(
//             bottomRight: Radius.circular(AppRadius.appBarRadius),
//             bottomLeft: Radius.circular(AppRadius.appBarRadius),
//           )),
//           title: Row(
//             crossAxisAlignment: CrossAxisAlignment.center,
//             children: [
//               Column(
//                 crossAxisAlignment: CrossAxisAlignment.start,
//                 children: [
//                   Text(
//                     title,
//                     style: context.whiteHeadLine
//                         .copyWith(fontSize: isHome ? null : 16),
//                   ).paddingOnly(top: AppSpaces.xSmallPadding + 3),
//                   context.smallGap,
//                   if (isHome)
//                     Text(
//                       const UserModel().currentUser.name ?? '',
//                       style: context.whiteLabelLarge,
//                     ),
//                 ],
//               ),
//             ],
//           ),
//           actions: [
//             if (isHome)
//               Row(
//                 children: [
//                   InkWell(
//                     onTap: () => context.to(const NotificationScreen()),
//                     child: Stack(
//                       alignment: Alignment.topRight,
//                       clipBehavior: Clip.none,
//                       children: [
//                         const IconWidget(icon: Assets.svgNotification),
//                         if (notificationCount != 0)
//                           Positioned(
//                             top: -5,
//                             right: -5,
//                             child: CircleAvatar(
//                               radius: 7,
//                               backgroundColor: Colors.red,
//                               child: Text(
//                                 notificationCount.toString(),
//                                 style: context.hint.copyWith(
//                                     fontSize: 10, color: ColorManager.white),
//                               ),
//                             ),
//                           )
//                       ],
//                     ),
//                   ),
//                   context.mediumGap,
//                   InkWell(
//                     onTap: () => context.to(const SettingsScreen()),
//                     child: const IconWidget(icon: Assets.svgParentSettings),
//                   ),
//                 ],
//               ).paddingOnly(right: 10, left: 10),
//           ],
//         );
//       },
//     );
//   }
//
//   @override
//   Size get preferredSize => Size.fromHeight(isHome ? 120 : 60);
// }
