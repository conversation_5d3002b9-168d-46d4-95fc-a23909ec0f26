import 'dart:developer';

import 'package:connectify_app/generated/assets.dart';
import 'package:connectify_app/src/screens/auth/repos/users_repo.dart';
import 'package:connectify_app/src/screens/auth/view/sign_in_screen/sign_in_screen.dart';
import 'package:connectify_app/src/screens/nursery/models/nursery_model.dart';
import 'package:connectify_app/src/screens/onboarding/controllers/onboarding_controller.dart';
import 'package:connectify_app/src/screens/onboarding/models/onboarding_model.dart';
import 'package:connectify_app/src/screens/onboarding/view/onboarding_screen.dart';
import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:xr_helper/xr_helper.dart';

class SplashScreen extends HookConsumerWidget {
  final bool fromLogout;

  const SplashScreen({super.key, this.fromLogout = false});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final onBoardingCtrl = ref.watch(onBoardingController(context));
    final onBoardingPage = useState<List<OnBoardingModel>>([]);

    void getOnBoardingData() async {
      final onBoardingPages = await onBoardingCtrl.getOnBoardingData();
      onBoardingPage.value = onBoardingPages;
    }

    // Update nursery data if user is logged in
    Future<void> updateNurseryData() async {
      try {
        final isLoggedIn = GetStorageService.getLocalData(key: LocalKeys.loggedIn) ?? false;

        if (isLoggedIn) {
          final userRepo = ref.read(userRepoProvider);
          final userData = GetStorageService.getLocalData(key: LocalKeys.user);

          if (userData != null && userData['nursery_model'] != null) {
            final nurseryId = userData['nursery_model']['id'];

            if (nurseryId != null) {
              log('Updating nursery data for ID: $nurseryId');

              final nurseryData = await userRepo.getNurseryById(id: nurseryId);

              if (nurseryData != null) {
                final nurseryModel = NurseryModel.fromAttributesJson(nurseryData);

                log('Updated nursery data: ${nurseryModel.toDataJson()}');

                await GetStorageService.setLocalData(
                  key: LocalKeys.nursery,
                  value: nurseryModel.toDataJson(),
                );

                log('Nursery data updated successfully');
              }
            }
          }
        }
      } catch (e) {
        log('Error updating nursery data: $e');
      }
    }

    final haveSeenOnBoarding =
        GetStorageService.getLocalData(key: LocalKeys.haveSeenOnBoarding) ??
            false;

    useEffect(() {
      // Update nursery data on app start
      updateNurseryData();

      if (!haveSeenOnBoarding) {
        getOnBoardingData();

        GetStorageService.setLocalData(
          key: LocalKeys.haveSeenOnBoarding,
          value: true,
        );

        // * Navigate to On Boarding after 3 seconds
        Future.delayed(const Duration(seconds: 3), () {
          if (context.mounted) {
            context.toReplacement(OnBoardingScreen(
              onBoardingPages: onBoardingPage.value,
            ));
          }
        });
      } else {
        Future.delayed(const Duration(seconds: 3), () {
          if (context.mounted) {
            context.toReplacement(const SignInScreen());
          }
        });
      }

      return;
    }, []);

    return Scaffold(
      body: Center(
        child: Image.asset(
          Assets.imagesSplash,
          height: context.height,
          width: context.width,
          fit: BoxFit.cover,
        ),
      ),
    );
  }
}
