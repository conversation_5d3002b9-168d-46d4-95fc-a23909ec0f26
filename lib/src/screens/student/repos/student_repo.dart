import 'dart:developer';

import 'package:connectify_app/src/screens/auth/models/user_model.dart';
import 'package:connectify_app/src/screens/class/models/class_model.dart';
import 'package:connectify_app/src/screens/student/models/student_model.dart';
import 'package:connectify_app/src/shared/data/remote/api_endpoints.dart';
import 'package:connectify_app/src/shared/data/remote/api_strings.dart';
import 'package:connectify_app/src/shared/providers/network_api_service_provider.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:xr_helper/xr_helper.dart';

final studentRepoProvider = Provider<StudentRepo>((ref) {
  final networkApiServices = ref.watch(networkServiceProvider);

  return StudentRepo(networkApiServices);
});

class StudentRepo with BaseRepository {
  final BaseApiServices _networkApiServices;

  StudentRepo(this._networkApiServices);

  // * Get Students ===================================
  Future<List<StudentModel>> getStudents() async {
    return await baseFunction(() async {
      final response =
          await _networkApiServices.getResponse(ApiEndpoints.students);

      final studentData = compute(responseToStudentModelList, response);
      return studentData;
    });
  }

  Future<StudentModel> getStudentById() async {
    return await baseFunction(() async {
      final response = await _networkApiServices
          .getResponse(ApiEndpoints.studentById(UserModel.studentId()));

      final studentData = StudentModel.fromJson(response[ApiStrings.data]);

      return studentData;
    });
  }

  //getStudentsByIds
  Future<List<StudentModel>> getStudentsByIds() async {
    return await baseFunction(() async {
      final ids =
          const UserModel().currentUser.studentIds.map((e) => e.$1!).toList();

      if (ids.isEmpty) {
        return [];
      }

      final url = ApiEndpoints.studentsByIds(ids);

      log('asfassasaf $url');

      final response = await _networkApiServices.getResponse(url);

      log('fffsff $response');

      final studentData = compute(responseToStudentModelList, response);
      return studentData;
    });
  }

  // *  Get Student By Id ===================================

  // Future<StudentModel> getStudentById() async {
  //   return await baseFunction(() async {
  //     final res = getStudents();
  //
  //     final student = res.then((value) =>
  //         value.firstWhere((element) => element.id == UserModel.studentId()));
  //
  //     return student;
  //   });
  // }

// * Add Students ===================================
  Future<void> addStudent(
      {required StudentModel student,
      required String pickedImage,
      required ClassModel? selectedClass}) async {
    return await baseFunction(() async {
      return await _networkApiServices.postResponse(
        ApiEndpoints.students,
        body: student.toJson(),
        filePaths: [pickedImage],
      );
    });
  }

// * Edit Students ===================================

  Future<void> editStudent({
    required StudentModel student,
    required int id,
  }) async {
    return await baseFunction(() async {
      return await _networkApiServices.putResponse(
          '${ApiEndpoints.editDeleteStudents}/$id',
          data: student.toJson());
    });
  }

  //? Active or DeActive Student Data ------------------------------------------
  Future<void> activeDeActiveStudent(
      {required int id, required bool isActive}) async {
    return await baseFunction(() async {
      await _networkApiServices
          .putResponse('${ApiEndpoints.editDeleteStudents}/$id', data: {
        ApiStrings.isActive: isActive,
      });
    });
  }

//? Delete Student Data ------------------------------------------
  Future<void> deleteStudent({required int id}) async {
    return await baseFunction(() async {
      await _networkApiServices
          .deleteResponse('${ApiEndpoints.editDeleteStudents}/$id');
    });
  }
}
