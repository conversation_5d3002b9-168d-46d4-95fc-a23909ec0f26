import 'package:connectify_app/src/screens/class/models/class_model.dart';
import 'package:connectify_app/src/shared/shared_models/base_media_model.dart';
import 'package:equatable/equatable.dart';
import 'package:xr_helper/xr_helper.dart';

import '../../../shared/data/remote/api_strings.dart';
import '../../../shared/shared_models/base_model.dart';

List<StudentModel> responseToStudentModelList(response) {
  final data = (response[ApiStrings.data] as List?) ?? [];

  final students =
      data.map((student) => StudentModel.fromJson(student)).toList();

  return students;
}

class StudentModel extends BaseModel {
  final ClassModel? classModel;
  final String? homeAddress;
  final String? motherPhoneNumber;
  final String? parentPhoneNumber;
  final DateTime? birthDate;
  final bool isActive;
  final String gender;
  final List<SubscriptionModel> subscriptions;
  final num? fees;
  final List<String>? pickupPersons;
  final DateTime? subscriptionDate;

  const StudentModel({
    super.id,
    super.name,
    super.image,
    super.createdAt,
    this.classModel,
    this.homeAddress,
    this.isActive = true,
    this.motherPhoneNumber,
    this.parentPhoneNumber,
    this.birthDate,
    this.fees,
    this.subscriptionDate,
    this.subscriptions = const [],
    this.pickupPersons = const [],
    this.gender = 'male',
  });

  factory StudentModel.fromJson(Map<String, dynamic> json) {
    // * -----------------------------------------------

    final attributes =
        json[ApiStrings.attributes] as Map<String, dynamic>? ?? {};

    if (attributes.isEmpty) {
      return const StudentModel();
    }

    // * -----------------------------------------------
    final image = attributes[ApiStrings.image] != null &&
            attributes[ApiStrings.image][ApiStrings.data] != null
        ? BaseMediaModel.fromJson(attributes[ApiStrings.image][ApiStrings.data]
            [ApiStrings.attributes])
        : null;
    //
    // // // * -----------------------------------------------
    final classModel = attributes[ApiStrings.classString] != null &&
            attributes[ApiStrings.classString][ApiStrings.data] != null
        ? ClassModel.fromStudentJson(
            attributes[ApiStrings.classString][ApiStrings.data])
        : null;

    // * -----------------------------------------------

    return StudentModel(
      id: json[ApiStrings.id],
      isActive: attributes[ApiStrings.isActive] ?? true,
      name: attributes[ApiStrings.name] ?? '',
      homeAddress: attributes[ApiStrings.homeAddress] ?? '',
      fees: attributes[ApiStrings.fees],
      image: image,
      classModel: classModel,
      gender: attributes[ApiStrings.gender] ?? 'male',
      motherPhoneNumber: attributes[ApiStrings.motherPhoneNumber] ?? '',
      parentPhoneNumber: attributes[ApiStrings.parentPhoneNumber] ?? '',
      subscriptions: (attributes[ApiStrings.subscriptions] as List? ?? [])
          .map((e) => SubscriptionModel.fromJson(e))
          .toList(),
      birthDate:
          attributes[ApiStrings.birthDate]?.toString().formatStringToDateTime,
      pickupPersons: attributes[ApiStrings.pickupPersons] != null
          ? (attributes[ApiStrings.pickupPersons] as List<dynamic>)
              .map<String>((e) => e[ApiStrings.person])
              .toList()
          : <String>[],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      // ApiStrings.name: name,
      // ApiStrings.classString: classModel?.id,
      // ApiStrings.birthDate: birthDate?.formatDateToString,
      // ApiStrings.isActive: isActive,
      // ApiStrings.gender: gender,
      // ApiStrings.fees: fees,
      if (homeAddress != null) ApiStrings.homeAddress: homeAddress,
      if (motherPhoneNumber != null)
        ApiStrings.motherPhoneNumber: motherPhoneNumber,
      if (parentPhoneNumber != null)
        ApiStrings.parentPhoneNumber: parentPhoneNumber,
      if (pickupPersons != null)
        ApiStrings.pickupPersons: pickupPersons!
            .map((e) => {
                  ApiStrings.person: e,
                })
            .toList(),
    };
  }

  @override
  List<Object?> get props => [
        id,
        name,
        image,
        createdAt,
        classModel,
        gender,
        homeAddress,
        motherPhoneNumber,
        parentPhoneNumber,
        birthDate,
        isActive,
        fees,
      ];

  //   super.id,
  //     super.name,
  //     super.image,
  //     super.createdAt,
  //     this.nursery,
  //     this.classModel,
  //     this.homeAddress,
  //     this.isActive = true,
  //     this.motherPhoneNumber,
  //     this.parentPhoneNumber,
  //     this.birthDate,
  //     this.fees,
  //     this.subscriptions = const [],
  //     this.gender = 'male',
//copyWith method
  StudentModel copyWith({
    int? id,
    String? name,
    BaseMediaModel? image,
    String? createdAt,
    ClassModel? classModel,
    String? homeAddress,
    String? motherPhoneNumber,
    String? parentPhoneNumber,
    DateTime? birthDate,
    bool? isActive,
    num? fees,
    String? gender,
    List<SubscriptionModel>? subscriptions,
  }) {
    return StudentModel(
      id: id ?? this.id,
      name: name ?? this.name,
      image: image ?? this.image,
      createdAt: createdAt ?? this.createdAt,
      classModel: classModel ?? this.classModel,
      homeAddress: homeAddress ?? this.homeAddress,
      motherPhoneNumber: motherPhoneNumber ?? this.motherPhoneNumber,
      parentPhoneNumber: parentPhoneNumber ?? this.parentPhoneNumber,
      birthDate: birthDate ?? this.birthDate,
      isActive: isActive ?? this.isActive,
      fees: fees ?? this.fees,
      gender: gender ?? this.gender,
      subscriptions: subscriptions ?? this.subscriptions,
    );
  }
}

class SubscriptionModel extends Equatable {
  final String date;
  final num amount;
  final bool isPaid;
  final bool isApproved;
  final String? paymentMethod;
  final BaseMediaModel? paymentScreenshot;

  const SubscriptionModel({
    this.date = '',
    this.amount = 0,
    this.isPaid = false,
    this.isApproved = false,
    this.paymentMethod,
    this.paymentScreenshot,
  });

  factory SubscriptionModel.fromJson(Map<String, dynamic> attributes) {
    final paymentScreenshot = attributes[ApiStrings.paymentScreenshot] !=
                null &&
            attributes[ApiStrings.paymentScreenshot][ApiStrings.data] != null
        ? BaseMediaModel.fromJson(attributes[ApiStrings.paymentScreenshot]
            [ApiStrings.data][ApiStrings.attributes])
        : null;

    return SubscriptionModel(
      date: attributes[ApiStrings.date]?.toString() ?? '',
      amount: attributes[ApiStrings.amount] ?? 0,
      isPaid: attributes[ApiStrings.isPaid] ?? false,
      isApproved: attributes[ApiStrings.isApproved] ?? false,
      paymentMethod: attributes[ApiStrings.paymentMethod],
      paymentScreenshot: paymentScreenshot,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      ApiStrings.date: date,
      ApiStrings.amount: amount,
      ApiStrings.isPaid: isPaid,
      ApiStrings.isApproved: isApproved,
      if (paymentMethod != null) ApiStrings.paymentMethod: paymentMethod,
      if (paymentScreenshot != null)
        ApiStrings.paymentScreenshot: paymentScreenshot!.id,
    };
  }

  SubscriptionModel copyWith({
    String? date,
    num? amount,
    bool? isPaid,
    bool? isApproved,
    String? paymentMethod,
    BaseMediaModel? paymentScreenshot,
  }) {
    return SubscriptionModel(
      date: date ?? this.date,
      amount: amount ?? this.amount,
      isPaid: isPaid ?? this.isPaid,
      isApproved: isApproved ?? this.isApproved,
      paymentMethod: paymentMethod ?? this.paymentMethod,
      paymentScreenshot: paymentScreenshot ?? this.paymentScreenshot,
    );
  }

  @override
  List<Object?> get props =>
      [date, amount, isPaid, isApproved, paymentMethod, paymentScreenshot];
}
