import 'package:connectify_app/src/screens/auth/models/user_model.dart';
import 'package:connectify_app/src/screens/student/models/student_model.dart';
import 'package:connectify_app/src/screens/student/repos/student_repo.dart';
import 'package:connectify_app/src/screens/student/view/student_screen/students_screen.dart';
import 'package:connectify_app/src/shared/data/remote/api_strings.dart';
import 'package:connectify_app/src/shared/extensions/context_extensions.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:xr_helper/xr_helper.dart';

// * Student Controller Provider ======================================
final studentControllerProvider =
    Provider.family<StudentController, BuildContext>((ref, context) {
  final studentRepo = ref.watch(studentRepoProvider);

  return StudentController(studentRepo: studentRepo, context);
});

// * Student Change Notifier Provider ======================================
final studentChangeNotifierProvider =
    ChangeNotifierProvider.family<StudentController, BuildContext>(
        (ref, context) {
  final studentRepo = ref.watch(studentRepoProvider);

  return StudentController(context, studentRepo: studentRepo);
});

// * Student Controller Future Provider ======================================
final getActiveStudents =
    FutureProvider.family<List<StudentModel>, BuildContext>(
        (ref, context) async {
  final studentController = ref.watch(studentControllerProvider(context));

  return await studentController.getStudents(context);
});

final getAllStudentsProvider =
    FutureProvider.family<List<StudentModel>, BuildContext>(
        (ref, context) async {
  final studentController = ref.watch(studentControllerProvider(context));

  return await studentController.getStudents(context,
      onlyActiveStudents: false);
});

final getStudentByIdProvider =
    FutureProvider.family<StudentModel, BuildContext>((ref, context) async {
  final studentController = ref.watch(studentControllerProvider(context));

  return await studentController.getStudentById();
});

final getStudentsByIdsProvider =
    FutureProvider.family<List<StudentModel>, BuildContext>(
        (ref, context) async {
  final studentController = ref.watch(studentControllerProvider(context));

  return await studentController.getStudentsByIds();
});

class StudentController extends BaseVM {
  final StudentRepo studentRepo;
  final BuildContext context;

  StudentController(this.context, {required this.studentRepo});

  // * Get Students ===================================

  Future<List<StudentModel>> getStudents(BuildContext context,
      {bool onlyActiveStudents = true}) async {
    return await baseFunction(context, () async {
      final studentData = await studentRepo.getStudents();

      //? add student with id 0

      if (const UserModel().isAdmin) {
        studentData.insert(
          0,
          const StudentModel(
            id: 0,
          ),
        );
      }

      // filter active students
      if (onlyActiveStudents) {
        return studentData.where((element) => element.isActive).toList();
      }

      return studentData;
    });
  }

  // * Get Student By Id ===================================
  Future<StudentModel> getStudentById() async {
    return await baseFunction(context, () async {
      final student = await studentRepo.getStudentById();
      return student;
    });
  }

  // * Get Students By Ids ===================================
  Future<List<StudentModel>> getStudentsByIds() async {
    return await baseFunction(context, () async {
      final students = await studentRepo.getStudentsByIds();
      return students;
    });
  }

  // * Add Students ===================================
  Future<void> addStudent({
    required Map<String, TextEditingController> controllers,
    required Map<String, ValueNotifier> valueNotifiers,
    required String pickedImage,
    required Widget navigateWidget,
  }) async {
    return await baseFunction(context, () async {
      final student = StudentModel(
        name: controllers[ApiStrings.name]!.text,
        homeAddress: controllers[ApiStrings.homeAddress]!.text,
        motherPhoneNumber: controllers[ApiStrings.motherPhoneNumber]!.text,
        parentPhoneNumber: controllers[ApiStrings.parentPhoneNumber]!.text,
        birthDate: valueNotifiers[ApiStrings.birthDate]!.value,
        classModel: valueNotifiers[ApiStrings.classString]!.value,
      );

      await studentRepo.addStudent(
        student: student,
        pickedImage: pickedImage,
        selectedClass: valueNotifiers[ApiStrings.classString]!.value,
      );
      if (context.mounted) {
        context.back();
        context.toReplacement(navigateWidget);
        context.showBarMessage(context.tr.addedSuccessfully);
      }
    });
  }

  // * Edit Students ===================================

  Future<void> editStudent(
      {required Map<String, TextEditingController> controllers,
      Map<String, ValueNotifier>? valueNotifiers,
      required Widget navigateWidget,
      List<TextEditingController> pickupControllers = const [],
      required int id}) async {
    return await baseFunction(context, () async {
      final student = StudentModel(
        homeAddress: controllers[ApiStrings.homeAddress]?.text,
        motherPhoneNumber: controllers[ApiStrings.motherPhoneNumber]?.text,
        parentPhoneNumber: controllers[ApiStrings.parentPhoneNumber]?.text,
        pickupPersons: pickupControllers
            .map((e) => e.text)
            .toList()
            .where((element) => element.isNotEmpty)
            .toList(),
      );

      await studentRepo.editStudent(
        student: student,
        id: id,
      );
      if (context.mounted) {
        context.back();
        context.toReplacement(navigateWidget);
        context.showBarMessage(context.tr.editSuccessfully);
      }
    });
  }

  //? Active or DeActive Student Data ------------------------------------------
  Future<void> activeOrDeActiveStudent(
      {required int id, required bool isActive}) async {
    return await baseFunction(context, () async {
      await studentRepo.activeDeActiveStudent(id: id, isActive: isActive);
      if (!context.mounted) return;

      context.toReplacement(const StudentsScreen());

      context.showBarMessage(context.tr.editSuccessfully);
    });
  }

  //? Delete Student Data ------------------------------------------
  Future<void> deleteStudent({
    required int id,
    required Widget navigateWidget,
  }) async {
    return await baseFunction(context, () async {
      await studentRepo.deleteStudent(id: id);
      if (context.mounted) {
        context.back();
        context.toReplacement(navigateWidget);
        context.showBarMessage(context.tr.deletedSuccessfully, isError: true);
      }
    },
        additionalFunction: (_) =>
            getStudents(context, onlyActiveStudents: false));
  }

  //? Pay Subscription ------------------------------------------
  Future<void> paySubscription({
    required int studentId,
    required List<SubscriptionModel> subscriptions,
    required Widget navigateWidget,
  }) async {
    return await baseFunction(context, () async {
      await studentRepo.paySubscription(
        studentId: studentId,
        subscriptions: subscriptions,
      );
      if (context.mounted) {
        context.back();
        context.toReplacement(navigateWidget);
        context.showBarMessage(context.tr.paymentSubmittedSuccessfully);
      }
    });
  }
}
