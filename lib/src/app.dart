import 'dart:developer';

import 'package:connectify_app/src/screens/auth/controllers/users_controller.dart';
import 'package:connectify_app/src/screens/auth/models/user_model.dart';
import 'package:connectify_app/src/screens/auth/view/sign_in_screen/sign_in_screen.dart';
import 'package:connectify_app/src/screens/home/<USER>/main_screen.dart';
import 'package:connectify_app/src/screens/nursery/models/nursery_model.dart';
import 'package:connectify_app/src/screens/nursery/models/nursery_model_helper.dart';
import 'package:connectify_app/src/screens/student/models/student_model.dart';
import 'package:connectify_app/src/shared/consts/app_constants.dart';
import 'package:connectify_app/src/shared/data/remote/api_endpoints.dart';
import 'package:connectify_app/src/shared/extensions/context_extensions.dart';
import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:in_app_update/in_app_update.dart';
import 'package:quickalert/models/quickalert_type.dart';
import 'package:quickalert/widgets/quickalert_dialog.dart';
import 'package:xr_helper/xr_helper.dart';

import '../generated/assets.dart';
import 'screens/splash_screen/view/splash_screen.dart';
import 'shared/services/app_settings/controller/settings_controller.dart';

class BaseApp extends HookConsumerWidget {
  final bool showSignIn;

  const BaseApp({super.key, this.showSignIn = false});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final usersController = ref.watch(userChangeNotifierProvider(context));
    final settingsController = ref.watch(settingsControllerProvider);
    final isUpdateRequired = useState(false);

    useEffect(() {
      Future<void> checkForUpdate() async {
        try {
          final updateInfo = await InAppUpdate.checkForUpdate();
          if (updateInfo.updateAvailability ==
                  UpdateAvailability.updateAvailable &&
              updateInfo.immediateUpdateAllowed) {
            await InAppUpdate.performImmediateUpdate();
          } else if (updateInfo.updateAvailability ==
              UpdateAvailability.updateAvailable) {
            isUpdateRequired.value = true;
          }
        } catch (e) {
          Log.e('Error checking for app updates: $e');
        }
      }

      checkForUpdate();
      return null;
    }, []);

    final user = const UserModel().currentUser;
    final signedUser = user != UserModel.empty();

    return Builder(
      builder: (context) {
        ScreenUtil.init(context);

        Log.w('SignedYYUser: $signedUser');

        return MaterialApp(
          debugShowCheckedModeBanner: false,
          debugShowMaterialGrid: false,
          locale: settingsController.locale,
          supportedLocales: AppConsts.supportedLocales,
          localizationsDelegates: AppConsts.localizationsDelegates,
          theme: ThemeManager().appTheme(),
          home: HookBuilder(
            builder: (BuildContext context) {
              if (isUpdateRequired.value) {
                return Scaffold(
                  body: Center(
                    child: Text(
                      context.tr.updateRequired,
                      textAlign: TextAlign.center,
                      style: context.title,
                    ),
                  ),
                );
              }

              final isNurserySubscriptionExpired = useState(false);

              final isLoading = useState(true);

              Future<void> getStudentById({required int? id}) async {
                try {
                  final response = await NetworkApiServices().getResponse(
                    ApiEndpoints.studentById(id),
                  );

                  final data =
                      response['data'] != null && response['data'].isNotEmpty
                          ? response['data']
                          : null;

                  if (data == null) return;

                  final nurseryModel = StudentModel.fromJson(data);

                  isNurserySubscriptionExpired.value =
                      nurseryModel.isActive == false;

                  isLoading.value = false;
                } catch (e) {
                  Log.e('Error fetching student data: $e');
                  isLoading.value = false;
                }
              }

              Future<void> getNurseryById({required int? id}) async {
                final response = await NetworkApiServices().getResponse(
                  ApiEndpoints.nurseryById(id),
                );

                final data =
                    response['data'] != null && response['data'].isNotEmpty
                        ? response['data']
                        : null;

                if (data == null) return;

                final nurseryModel = NurseryModel.fromAttributesJson(data);

                log('Updated nursery data: ${nurseryModel.toDataJson()}');

                await GetStorageService.setLocalData(
                  key: LocalKeys.nursery,
                  value: nurseryModel.toDataJson(),
                );

                log('Nursery data updated successfully');

                isNurserySubscriptionExpired.value =
                    nurseryModel.endDate?.isBefore(DateTime.now()) ?? false;

                if (isNurserySubscriptionExpired.value) {
                  isLoading.value = false;

                  return;
                }

                await getStudentById(id: UserModel.studentId());

                await usersController.getUserById(
                    id: const UserModel().currentUser.id!);
              }

              useEffect(() {
                if (signedUser) {
                  getNurseryById(id: NurseryModelHelper.currentNurseryId())
                      .then(
                    (value) {
                      if (isNurserySubscriptionExpired.value) {
                        QuickAlert.show(
                          context: context,
                          title: context.tr.warning,
                          disableBackBtn: true,
                          text: context
                              .tr.subscriptionExpiredPleaseContactSupport,
                          type: QuickAlertType.info,
                          showConfirmBtn: false,
                          showCancelBtn: true,
                          cancelBtnText: context.tr.cancel,
                          // confirmBtnText: context.tr.contactSupport,
                          // onConfirmBtnTap: () {
                          //   launchUrl(
                          //     Uri.parse('https://wa.me/201014878502'),
                          //   );
                          // },
                        );
                      }
                    },
                  );
                } else {
                  isLoading.value = false;
                }

                return;
              }, []);

              if (isLoading.value || isNurserySubscriptionExpired.value) {
                return Scaffold(
                  body: Center(
                    child: Image.asset(
                      Assets.imagesSplash,
                      height: context.height,
                      width: context.width,
                      fit: BoxFit.cover,
                    ),
                  ),
                );
              }

              if (showSignIn) {
                return const SignInScreen();
              }

              if (signedUser) {
                return const MainScreen();
              } else {
                return const SplashScreen();
              }
            },
          ),
        );
      },
    );
  }
}

// import 'package:connectify_app/src/screens/auth/models/user_model.dart';
// import 'package:connectify_app/src/screens/auth/view/sign_in_screen/sign_in_screen.dart';
// import 'package:connectify_app/src/screens/home/<USER>/main_screen.dart';
// import 'package:connectify_app/src/shared/consts/app_constants.dart';
// import 'package:flutter/material.dart';
// import 'package:flutter_riverpod/flutter_riverpod.dart';
// import 'package:flutter_screenutil/flutter_screenutil.dart';
// import 'package:xr_helper/xr_helper.dart';
//
// import 'screens/splash_screen/view/splash_screen.dart';
// import 'shared/services/app_settings/controller/settings_controller.dart';
//
// //?====================================================================
// //!====================== My App ======================================
// // * ==================================================================
// class BaseApp extends ConsumerWidget {
//   final bool showSignIn;
//
//   const BaseApp({super.key, this.showSignIn = false});
//
//   @override
//   Widget build(BuildContext context, WidgetRef ref) {
//     final settingsController = ref.watch(settingsControllerProvider);
//
//     final user = const UserModel().currentUser;
//
//     final signedUser = user != UserModel.empty();
//
//     return Builder(builder: (context) {
//       ScreenUtil.init(context);
//
//       Log.w('SignedYYUser: $signedUser');
//
//       return MaterialApp(
//         debugShowCheckedModeBanner: false,
//         debugShowMaterialGrid: false,
//         //? Localization
//         locale: settingsController.locale,
//         supportedLocales: AppConsts.supportedLocales,
//         localizationsDelegates: AppConsts.localizationsDelegates,
//         //? Theme
//         theme: ThemeManager().appTheme(),
//         home: Builder(
//           builder: (BuildContext context) {
//             if (showSignIn) {
//               return const SignInScreen();
//             }
//             if (signedUser) {
//               return const MainScreen();
//             } else {
//               return const SplashScreen();
//             }
//           },
//         ),
//       );
//     });
//   }
// }
