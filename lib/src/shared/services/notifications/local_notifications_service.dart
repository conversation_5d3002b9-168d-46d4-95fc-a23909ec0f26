import 'package:awesome_notifications/awesome_notifications.dart';
import 'package:xr_helper/xr_helper.dart';

import '../../../screens/student/models/student_model.dart';
import '../../consts/app_constants.dart';

class LocalNotificationsService {
  static int createUniqueId(String prayerName, DateTime dateTime) {
    final id = prayerName.hashCode ^ dateTime.hashCode;
    return id.abs();
  }

  static Future<void> init() async {
    final isAllowed = await AwesomeNotifications().isNotificationAllowed();

    if (!isAllowed) {
      await AwesomeNotifications().requestPermissionToSendNotifications(
        permissions: [
          NotificationPermission.Alert,
          NotificationPermission.Badge,
          NotificationPermission.Sound,
        ],
      );
    }

    await AwesomeNotifications().initialize(
      'resource://mipmap/ic_launcher',
      // debug: kDebugMode,
      [
        NotificationChannel(
          channelKey: AppConsts.subscriptionReminderChannelKey,
          channelName: 'Subscription Reminder Notifications',
          channelDescription:
              'Notification channel for subscription reminder notifications',
          // defaultColor: const Color(0xFFFFFFFF),
          // ledColor: Colors.white,
          importance: NotificationImportance.Max,
          playSound: true,
        ),
      ],
    );
  }

  static Future<bool> createScheduleNotification({
    required int id,
    required DateTime dateTime,
    bool isReminder = false,
    String? title,
    String? body,
    String? sound,
  }) async {
    // if (kDebugMode) return true;

    final isAllowed = await AwesomeNotifications().isNotificationAllowed();

    const channelKey = AppConsts.subscriptionReminderChannelKey;

    final timeZone = await AwesomeNotifications().getLocalTimeZoneIdentifier();

    if (isAllowed) {
      Log.w('Creating_Schedule_Notification: $id\n'
          'Title: $title\n'
          'Body: $body\n'
          'DateTime: ${dateTime.formatDateToTimeAndString}\n'
          'ChannelKey: $channelKey\n'
          // 'Sound: $sound\n'
          'TimeZone: $timeZone');
      return await AwesomeNotifications().createNotification(
        content: NotificationContent(
          id: id,
          channelKey: channelKey,
          criticalAlert: true,
          category: NotificationCategory.Status,
          title: title,
          body: body,
          payload: {
            'scheduledTime': dateTime.toIso8601String(),
          },
        ),
        schedule: NotificationCalendar(
          day: dateTime.day,
          month: dateTime.month,
          year: dateTime.year,
          hour: dateTime.hour,
          minute: dateTime.minute,
          second: 0,
          allowWhileIdle: true,
          repeats: false,
          timeZone: timeZone,
        ),
      );
    } else {
      await AwesomeNotifications().requestPermissionToSendNotifications();
    }

    return isAllowed;
  }

  static Future<void> cancelScheduledNotificationById(int id) async {
    await AwesomeNotifications().cancel(id);
  }

  static Future<void> cancelAllScheduledNotifications() async {
    await AwesomeNotifications().cancelAll();
    await AwesomeNotifications().cancelAllSchedules();
    Log.w('Cancelled all scheduled notifications');
  }

  //listScheduledNotifications
  static Future<List<NotificationModel>> listScheduledNotifications() async {
    return await AwesomeNotifications().listScheduledNotifications();
  }

  static Future<void>
      clearAppNotificationsIfNoScheduledAzanOrReminders() async {
    final now = DateTime.now();
    const bufferDuration = Duration(minutes: 3);

    // Retrieve all scheduled notifications
    final scheduledNotifications = await listScheduledNotifications();

    // Check if any scheduled notification is within the 3-minute window
    final hasUpcomingNotifications = scheduledNotifications.any((notification) {
      final scheduledTime = DateTime.tryParse(
          notification.content?.payload?['scheduledTime'] ?? '');

      if (scheduledTime == null) return false;

      return scheduledTime.isAfter(now.subtract(bufferDuration)) &&
          scheduledTime.isBefore(now.add(bufferDuration));
    });

    // Clear notifications only if no upcoming notifications
    if (!hasUpcomingNotifications) {
      await cancelAllScheduledNotifications();
      Log.w(
          'Cleared all app notifications as no upcoming reminders are scheduled.');
    } else {
      Log.w('Skipped clearing notifications due to upcoming reminders.');
    }
  }

  // Subscription reminder methods
  static int createSubscriptionReminderId(
      int studentId, DateTime subscriptionDate) {
    final id =
        'subscription_${studentId}_${subscriptionDate.year}_${subscriptionDate.month}'
            .hashCode;
    return id.abs();
  }

  static Future<bool> scheduleSubscriptionReminder({
    required StudentModel student,
    required String title,
    required String body,
  }) async {
    if (student.id == null) {
      Log.w('Cannot schedule subscription reminder: missing student ID');
      return false;
    }

    final now = DateTime.now();

    // Calculate next payment date based on last subscription or current date
    DateTime nextPaymentDate;

    if (student.subscriptions.isNotEmpty) {
      // Get the latest subscription date
      final latestSubscription = student.subscriptions
          .where((sub) => sub.date.isNotEmpty)
          .map((sub) => sub.date.formatStringToDateTime)
          .reduce((a, b) => a.isAfter(b) ? a : b);

      // Next payment is one month after the latest subscription
      nextPaymentDate = DateTime(
        latestSubscription.year,
        latestSubscription.month + 1,
        latestSubscription.day,
      );
    } else {
      // If no subscriptions, use current date as base
      nextPaymentDate = DateTime(now.year, now.month, now.day);
    }

    // Schedule reminder for 9 PM the day before payment is due
    DateTime reminderDate = DateTime(
      nextPaymentDate.year,
      nextPaymentDate.month,
      nextPaymentDate.day - 1,
      21, // 9 PM
      0,
      0,
    );

    // If the reminder date has already passed, schedule for next month
    if (reminderDate.isBefore(now)) {
      nextPaymentDate = DateTime(
        nextPaymentDate.year,
        nextPaymentDate.month + 1,
        nextPaymentDate.day,
      );

      reminderDate = DateTime(
        nextPaymentDate.year,
        nextPaymentDate.month,
        nextPaymentDate.day - 1,
        21, // 9 PM
        0,
        0,
      );
    }

    final notificationId = createSubscriptionReminderId(student.id!, nextPaymentDate);

    Log.w(
        'Scheduling subscription reminder for ${student.name} on ${reminderDate.formatDateToTimeAndString} (payment due: ${nextPaymentDate.formatDateToString})');

    return await createScheduleNotification(
      id: notificationId,
      dateTime: reminderDate,
      title: title,
      body: body,
    );
  }

  static Future<void> cancelSubscriptionReminder({
    required int studentId,
    required DateTime subscriptionDate,
  }) async {
    final notificationId =
        createSubscriptionReminderId(studentId, subscriptionDate);
    await cancelScheduledNotificationById(notificationId);
    Log.w('Cancelled subscription reminder for student $studentId');
  }

  static Future<void> rescheduleSubscriptionReminder({
    required StudentModel student,
    required String title,
    required String body,
  }) async {
    if (student.id == null) return;

    // Cancel existing reminder
    await cancelSubscriptionReminder(
      studentId: student.id!,
      subscriptionDate: DateTime.now(), // Use current date as fallback
    );

    // Schedule new reminder
    await scheduleSubscriptionReminder(
      student: student,
      title: title,
      body: body,
    );
  }

  // Schedule reminders for multiple students
  static Future<void> scheduleSubscriptionRemindersForStudents({
    required List<StudentModel> students,
  }) async {
    for (final student in students) {
      if (student.id == null) continue;

      // Check if student needs payment reminder
      if (_shouldScheduleReminderForStudent(student)) {
        final title = students.length == 1
            ? 'Payment Reminder'
            : 'Payment Reminder - ${student.name}';

        final body = students.length == 1
            ? 'Your subscription payment is due tomorrow. Please make your payment.'
            : '${student.name}\'s subscription payment is due tomorrow. Please make the payment.';

        await scheduleSubscriptionReminder(
          student: student,
          title: title,
          body: body,
        );
      }
    }
  }

  // Helper method to check if student needs payment reminder
  static bool _shouldScheduleReminderForStudent(StudentModel student) {
    // Check if student subscriptionDate is null
    if (student.subscriptionDate == null) {
      return false;
    }

    if (student.subscriptions.isEmpty) {
      return false; // No subscription history, don't schedule reminder
    }

    final now = DateTime.now();
    final cutoffDate = DateTime(2025, 5, 15); // May 15, 2025

    // Get valid subscriptions
    final validSubscriptions = student.subscriptions
        .where((sub) => sub.date.isNotEmpty)
        .toList();

    if (validSubscriptions.isEmpty) {
      return false;
    }

    // Get the latest subscription
    final latestSubscription = validSubscriptions
        .map((sub) => sub.date.formatStringToDateTime)
        .reduce((a, b) => a.isAfter(b) ? a : b);

    // If latest subscription is before cutoff date, don't schedule reminder
    if (latestSubscription.isBefore(cutoffDate)) {
      return false;
    }

    // Check if payment is due (more than a month since last payment)
    final nextPaymentDate = DateTime(
      latestSubscription.year,
      latestSubscription.month + 1,
      latestSubscription.day,
    );

    return nextPaymentDate.isBefore(now.add(const Duration(days: 2))); // Due within 2 days
  }
}
