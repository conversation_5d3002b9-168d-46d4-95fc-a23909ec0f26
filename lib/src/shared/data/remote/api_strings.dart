class ApiStrings {
  //? Base
  static const data = 'data';
  static const attributes = 'attributes';
  static const id = 'id';
  static const classId = 'class_id';
  static const studentsResult = 'students_result';
  static const pickupPersons = 'pickup_persons';
  static const question = 'question';
  static const createdAt = 'createdAt';
  static const endDate = 'end_date';

  //? OnBoarding
  static const onBoarding = 'parent_on_boarding';
  static const title = 'title';
  static const subtitle = 'subtitle';
  static const image = 'image';
  static const String classTeachers = 'class_teachers';

  //? Classes
  static const name = 'name';
  static const logo = 'logo';
  static const String teacherClasses = 'teacher_classes';

  static const String products = 'products';
  static const String classString = 'class';
  static const String sections = 'sections';
  static const String note = 'note';
  static const String media = 'media';
  static const String activityNotes = 'activity_notes';
  static const meal = 'meal';
  static const String isWeekly = 'is_weekly_activity';
  static const String selectedClasses = 'selected_classes';

  //? Students
  static const String homeAddress = 'home_address';
  static const String subscriptionDate = 'subscription_date';
  static const String markAsSent = 'mark_as_sent';
  static const String motherPhoneNumber = 'mother_phone_number';
  static const String parentPhoneNumber = 'parent_phone_number';
  static const String subscriptions = 'subscriptions';
  static const String birthDate = 'birth_date';

  //? Fields
  static const String user = 'user';
  static const String isActive = 'is_active';
  static const String fees = 'fees';
  static const String gender = 'gender';
  static const String person = 'person';
  static const String date = 'date';
  static const String eventName = 'event_name';
  static const String eventType = 'event_type';
  static const String amount = 'amount';
  static const String isPaid = 'is_paid';
  static const String attendanceDate = 'attendance_date';
  static const String jobTitle = 'job_title';
  static const String nursery = 'nursery';
  static const String classStudents = 'class_students';
  static const String students = 'students';
  static const String student = 'student';
  static const String rate = 'rate';
  static const String photo = 'photo';
  static const String identifier = 'identifier';
  static const String password = 'password';
  static const String studentIds = 'student_ids';
  static const String type = 'type';
  static const String body = 'body';
  static const String topic = 'topic';
  static const String admin = 'admin';
  static const String teacher = 'teacher';
  static const String teachers = 'teachers';
  static const String parent = 'parent';
  static const String child = 'child';
  static const String username = 'username';
  static const String displayName = 'displayName';
  static const String deviceToken = 'device_token';
  static const String config = 'config';
  static const String currencies = 'currencies';
  static const String currencyEn = 'currency_en';
  static const String currencyAr = 'currency_ar';
  static const String symbol = 'symbol';
  static const String email = 'email';
  static const String phone = 'phone';
  static const String featureImage = 'feature_image';
  static const String description = 'description';
  static const String startTime = 'start_time';
  static const String endTime = 'endTime';
  static const String food = 'food';
  static const String sleep = 'sleep';
  static const String toilet = 'toilet';
  static const String day = 'day';
  static const String studentId = 'student_id';
  static const String nurseryId = 'nursery_id';
  static const String from = 'from';
  static const String to = 'to';
  static const String activity = 'activity';
  static const String mealType = 'meal_type';
  static const String mealAmount = 'meal_amount';
  static const String toiletType = 'toilet_type';
  static const String toiletWay = 'toilet_way';
  static const String supply = 'supply';
  static const String supplies = 'supplies';
  static const String maxStudents = 'max_students';
  static const String canContactTeacher = 'can_contact_teacher';
  static const String showNurseryLogoInParentApp =
      'show_nursery_logo_in_parent_app';
  static const String target = 'target';
  static const String isApproved = 'is_approved';
  static const String paymentMethod = 'payment_method';
  static const String paymentScreenshot = 'payment_screenshot';
  static const String paymentMethods = 'payment_methods';
  static const String instapay = 'instapay';
  static const String vodafoneCash = 'vodafone_cash';
  static const String etisalatCash = 'etisalat_cash';
  static const String weCash = 'we_cash';
  static const String orangeCash = 'orange_cash';

  //? Fields

  static const String fcmToken = 'fcm_token';
  static const String passwordConfirmation = 'passwordConfirmation';
}
