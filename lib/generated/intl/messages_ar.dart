// DO NOT EDIT. This is code generated via package:intl/generate_localized.dart
// This is a library that provides messages for a ar locale. All the
// messages from the main program should be duplicated here with the same
// function name.

// Ignore issues from commonly used lints in this file.
// ignore_for_file:unnecessary_brace_in_string_interps, unnecessary_new
// ignore_for_file:prefer_single_quotes,comment_references, directives_ordering
// ignore_for_file:annotate_overrides,prefer_generic_function_type_aliases
// ignore_for_file:unused_import, file_names, avoid_escaping_inner_quotes
// ignore_for_file:unnecessary_string_interpolations, unnecessary_string_escapes

import 'package:intl/intl.dart';
import 'package:intl/message_lookup_by_library.dart';

final messages = new MessageLookup();

typedef String MessageIfAbsent(String messageStr, List<dynamic> args);

class MessageLookup extends MessageLookupByLibrary {
  String get localeName => 'ar';

  static String m0(name) => "إرسال رسالة جديدة إلى ${name}";

  static String m1(Student) => "${Student} is absent today";

  final messages = _notInlinedMessages(_notInlinedMessages);
  static Map<String, Function> _notInlinedMessages(_) => <String, Function>{
    "Of": MessageLookupByLibrary.simpleMessage("من"),
    "SignupAsa": MessageLookupByLibrary.simpleMessage("التسجيل كـ"),
    "SkipForNow": MessageLookupByLibrary.simpleMessage("تخطي الآن"),
    "absent": MessageLookupByLibrary.simpleMessage("غائب"),
    "active": MessageLookupByLibrary.simpleMessage("نشط"),
    "activities": MessageLookupByLibrary.simpleMessage("الأنشطة"),
    "activitiesCompleted": MessageLookupByLibrary.simpleMessage(
      "الأنشطة المكتملة",
    ),
    "activityChart": MessageLookupByLibrary.simpleMessage("مخطط النشاط"),
    "activityDescription": MessageLookupByLibrary.simpleMessage("وصف النشاط"),
    "activityName": MessageLookupByLibrary.simpleMessage("اسم النشاط"),
    "add": MessageLookupByLibrary.simpleMessage("إضافة"),
    "addANewStaffMember": MessageLookupByLibrary.simpleMessage(
      "إضافة عضو فريق جديد",
    ),
    "addNewBill": MessageLookupByLibrary.simpleMessage("إضافة فاتورة جديدة"),
    "addNewClass": MessageLookupByLibrary.simpleMessage("إضافة فصل جديد"),
    "addNewEvent": MessageLookupByLibrary.simpleMessage("إضافة حدث جديد"),
    "addNewInvoice": MessageLookupByLibrary.simpleMessage("إضافة فاتورة جديدة"),
    "addNewQuestion": MessageLookupByLibrary.simpleMessage("إضافة سؤال جديد"),
    "addNewStudents": MessageLookupByLibrary.simpleMessage("إضافة طلاب جدد"),
    "addNurseryActivities": MessageLookupByLibrary.simpleMessage(
      "إضافة نشاط جديد",
    ),
    "addNurseryTeam": MessageLookupByLibrary.simpleMessage(
      "إضافة فريق الحضانة",
    ),
    "addNurseryTeamMember": MessageLookupByLibrary.simpleMessage(
      "إضافة عضو فريق جديد",
    ),
    "addParentsPhoneNumber": MessageLookupByLibrary.simpleMessage(
      "إضافة رقم هاتف الوالدين",
    ),
    "addPickupPerson": MessageLookupByLibrary.simpleMessage(
      "إضافة شخص الاستلام",
    ),
    "addStudents": MessageLookupByLibrary.simpleMessage("إضافة الطلاب"),
    "addTheEmergency": MessageLookupByLibrary.simpleMessage(
      "أضف البيانات الطارئة التي تساعد الحضانة على الوصول إليك",
    ),
    "addedSuccessfully": MessageLookupByLibrary.simpleMessage(
      "تمت الإضافة بنجاح",
    ),
    "address": MessageLookupByLibrary.simpleMessage("العنوان"),
    "admin": MessageLookupByLibrary.simpleMessage("المسؤول"),
    "adminSignUp": MessageLookupByLibrary.simpleMessage("تسجيل المسؤول"),
    "administrator": MessageLookupByLibrary.simpleMessage("مسؤول"),
    "all": MessageLookupByLibrary.simpleMessage("الكل"),
    "alreadyHaveAnAccount": MessageLookupByLibrary.simpleMessage(
      "هل لديك حساب بالفعل؟",
    ),
    "announcements": MessageLookupByLibrary.simpleMessage("الإعلانات"),
    "arabic": MessageLookupByLibrary.simpleMessage("العربية"),
    "areYouSureToDeleteThisActivity": MessageLookupByLibrary.simpleMessage(
      "هل أنت متأكد من حذف هذا النشاط؟",
    ),
    "areYouSureToDeleteThisBill": MessageLookupByLibrary.simpleMessage(
      "هل أنت متأكد من حذف هذه الفاتورة؟",
    ),
    "areYouSureToDeleteThisClass": MessageLookupByLibrary.simpleMessage(
      "هل أنت متأكد من حذف هذا الفصل؟",
    ),
    "areYouSureToDeleteThisEvent": MessageLookupByLibrary.simpleMessage(
      "هل أنت متأكد من حذف هذا الحدث؟",
    ),
    "areYouSureToDeleteThisInvoice": MessageLookupByLibrary.simpleMessage(
      "هل أنت متأكد من حذف هذه الفاتورة؟",
    ),
    "areYouSureToDeleteThisQuestion": MessageLookupByLibrary.simpleMessage(
      "هل أنت متأكد من حذف هذا السؤال؟",
    ),
    "areYouSureToDeleteThisStudent": MessageLookupByLibrary.simpleMessage(
      "هل أنت متأكد من حذف هذا الطالب؟",
    ),
    "areYouSureToDeleteThisSupply": MessageLookupByLibrary.simpleMessage(
      "هل أنت متأكد من حذف هذه المستلزمات؟",
    ),
    "areYouSureToDeleteThisTeacher": MessageLookupByLibrary.simpleMessage(
      "هل أنت متأكد من حذف هذا المدرس؟",
    ),
    "areYouSureToDeleteYourAccount": MessageLookupByLibrary.simpleMessage(
      "هل أنت متأكد من حذف هذا الحساب؟",
    ),
    "assign": MessageLookupByLibrary.simpleMessage("تعيين"),
    "assignActivityToClass": MessageLookupByLibrary.simpleMessage(
      "تعيين النشاط للفصل",
    ),
    "assignSupplyToStudent": MessageLookupByLibrary.simpleMessage(
      "تعيين المستلزمات للطالب",
    ),
    "assignToClass": MessageLookupByLibrary.simpleMessage("تعيين إلى الفصل"),
    "assigned": MessageLookupByLibrary.simpleMessage("تم التعيين"),
    "attendance": MessageLookupByLibrary.simpleMessage("الحضور"),
    "attendanceChart": MessageLookupByLibrary.simpleMessage("مخطط الحضور"),
    "attendanceTracking": MessageLookupByLibrary.simpleMessage("تتبع الحضور"),
    "attended": MessageLookupByLibrary.simpleMessage("حضر"),
    "attendeesOfToday": MessageLookupByLibrary.simpleMessage("الحضور اليوم"),
    "back": MessageLookupByLibrary.simpleMessage("رجوع"),
    "billAmount": MessageLookupByLibrary.simpleMessage("مبلغ الفاتورة"),
    "billName": MessageLookupByLibrary.simpleMessage("اسم الفاتورة"),
    "bills": MessageLookupByLibrary.simpleMessage("الفواتير"),
    "billsChart": MessageLookupByLibrary.simpleMessage("مخطط الفواتير"),
    "birthDate": MessageLookupByLibrary.simpleMessage("تاريخ الميلاد"),
    "breakfast": MessageLookupByLibrary.simpleMessage("الإفطار"),
    "cancel": MessageLookupByLibrary.simpleMessage("إلغاء"),
    "changeLanguage": MessageLookupByLibrary.simpleMessage("تغيير اللغة"),
    "changePassword": MessageLookupByLibrary.simpleMessage("تغيير كلمة المرور"),
    "classDescription": MessageLookupByLibrary.simpleMessage("وصف الفصل"),
    "className": MessageLookupByLibrary.simpleMessage("اسم الفصل"),
    "classes": MessageLookupByLibrary.simpleMessage("الفصول"),
    "completeVerification": MessageLookupByLibrary.simpleMessage(
      "إكمال التحقق",
    ),
    "confirm": MessageLookupByLibrary.simpleMessage("تأكيد"),
    "confirmNewPassword": MessageLookupByLibrary.simpleMessage(
      "تأكيد كلمة المرور الجديدة",
    ),
    "confirmation": MessageLookupByLibrary.simpleMessage("التأكيد"),
    "congratulations": MessageLookupByLibrary.simpleMessage("تهانينا"),
    "contactSupport": MessageLookupByLibrary.simpleMessage("الاتصال بالدعم"),
    "createANewClass": MessageLookupByLibrary.simpleMessage("إنشاء فصل جديد"),
    "createANewSupply": MessageLookupByLibrary.simpleMessage(
      "إضافة مستلزمات جديدة",
    ),
    "createNewClass": MessageLookupByLibrary.simpleMessage("إنشاء فصل جديد"),
    "currentActivity": MessageLookupByLibrary.simpleMessage("النشاط الحالي"),
    "currentMonth": MessageLookupByLibrary.simpleMessage("الشهر الحالي"),
    "dailySchedule": MessageLookupByLibrary.simpleMessage("الجدول اليومي"),
    "dashboard": MessageLookupByLibrary.simpleMessage("لوحة التحكم"),
    "date": MessageLookupByLibrary.simpleMessage("التاريخ"),
    "day": MessageLookupByLibrary.simpleMessage("اليوم"),
    "delete": MessageLookupByLibrary.simpleMessage("حذف"),
    "deleteAccount": MessageLookupByLibrary.simpleMessage("حذف الحساب"),
    "deletedSuccessfully": MessageLookupByLibrary.simpleMessage(
      "تم الحذف بنجاح",
    ),
    "description": MessageLookupByLibrary.simpleMessage("الوصف"),
    "didNotGetCode": MessageLookupByLibrary.simpleMessage(
      "ألم تحصل على الرمز؟",
    ),
    "dontHaveAnAccount": MessageLookupByLibrary.simpleMessage("ليس لديك حساب؟"),
    "downloadedSuccessfully": MessageLookupByLibrary.simpleMessage(
      "تم التحميل بنجاح",
    ),
    "edit": MessageLookupByLibrary.simpleMessage("تحرير"),
    "editClass": MessageLookupByLibrary.simpleMessage("تحرير الفصل"),
    "editEvent": MessageLookupByLibrary.simpleMessage("تحرير الحدث"),
    "editSuccessfully": MessageLookupByLibrary.simpleMessage(
      "تم التحرير بنجاح",
    ),
    "editTeacher": MessageLookupByLibrary.simpleMessage("تحرير المدرس"),
    "email": MessageLookupByLibrary.simpleMessage("البريد الإلكتروني"),
    "emergency": MessageLookupByLibrary.simpleMessage("الطوارئ"),
    "emergencyInformation": MessageLookupByLibrary.simpleMessage(
      "معلومات الطوارئ",
    ),
    "english": MessageLookupByLibrary.simpleMessage("الإنجليزية"),
    "enter": MessageLookupByLibrary.simpleMessage("أدخل"),
    "enterNewPassword": MessageLookupByLibrary.simpleMessage(
      "أدخل كلمة مرور جديدة",
    ),
    "enterOtp": MessageLookupByLibrary.simpleMessage("أدخل رمز OTP"),
    "enterPhoneNumberFirst": MessageLookupByLibrary.simpleMessage(
      "أدخل رقم هاتفك أولاً",
    ),
    "enterPickupPerson": MessageLookupByLibrary.simpleMessage(
      "أدخل شخص الاستلام",
    ),
    "enterValidPhoneNumber": MessageLookupByLibrary.simpleMessage(
      "أدخل رقم هاتف صالح",
    ),
    "errorOccurred": MessageLookupByLibrary.simpleMessage("حدث خطأ"),
    "eventName": MessageLookupByLibrary.simpleMessage("اسم الحدث"),
    "eventThisMonth": MessageLookupByLibrary.simpleMessage("حدث هذا الشهر"),
    "eventType": MessageLookupByLibrary.simpleMessage("نوع الحدث"),
    "events": MessageLookupByLibrary.simpleMessage("الأحداث"),
    "exams": MessageLookupByLibrary.simpleMessage("الامتحانات"),
    "father": MessageLookupByLibrary.simpleMessage("الأب"),
    "financial": MessageLookupByLibrary.simpleMessage("المالية"),
    "finishLetsStart": MessageLookupByLibrary.simpleMessage("إنهاء، لنبدأ"),
    "food": MessageLookupByLibrary.simpleMessage("الطعام"),
    "forgetPassword": MessageLookupByLibrary.simpleMessage("نسيت كلمة المرور"),
    "friday": MessageLookupByLibrary.simpleMessage("الجمعة"),
    "from": MessageLookupByLibrary.simpleMessage("من"),
    "getTalkingFrom": MessageLookupByLibrary.simpleMessage(
      "كل يوم هو رحلة جديده\n سجل دخول للانضمام إلينا.",
    ),
    "goodAfternoon": MessageLookupByLibrary.simpleMessage("مساء الخير!"),
    "goodMorning": MessageLookupByLibrary.simpleMessage("صباح الخير!"),
    "home": MessageLookupByLibrary.simpleMessage("الرئيسية"),
    "homeAddress": MessageLookupByLibrary.simpleMessage("العنوان"),
    "iHaveReadThe": MessageLookupByLibrary.simpleMessage("لقد قرأت"),
    "inClothes": MessageLookupByLibrary.simpleMessage("الملابس"),
    "inTheDiaper": MessageLookupByLibrary.simpleMessage("الحفاض"),
    "inTheToilet": MessageLookupByLibrary.simpleMessage("الحمام"),
    "incomeChart": MessageLookupByLibrary.simpleMessage("مخطط الدخل"),
    "invoiceAmount": MessageLookupByLibrary.simpleMessage("مبلغ الفاتورة"),
    "invoiceName": MessageLookupByLibrary.simpleMessage("اسم الفاتورة"),
    "invoices": MessageLookupByLibrary.simpleMessage("الفواتير"),
    "invoicesChart": MessageLookupByLibrary.simpleMessage("مخطط الفواتير"),
    "letsDoAGreatJob": MessageLookupByLibrary.simpleMessage(
      "دعونا نقوم بعمل رائع",
    ),
    "letsStart": MessageLookupByLibrary.simpleMessage("لنبدأ"),
    "logout": MessageLookupByLibrary.simpleMessage("تسجيل الخروج"),
    "lunch": MessageLookupByLibrary.simpleMessage("الغداء"),
    "markAsRead": MessageLookupByLibrary.simpleMessage("وضع علامة كمقروء"),
    "markAsSent": MessageLookupByLibrary.simpleMessage("وضع علامة كمرسل"),
    "matherPhoneNumber": MessageLookupByLibrary.simpleMessage("رقم هاتف الأم"),
    "maxStudentsReachedPleaseContactSupport":
        MessageLookupByLibrary.simpleMessage(
          "تم الوصول إلى الحد الأقصى للطلاب، يرجى الاتصال بالدعم!",
        ),
    "maxUploadFileSizeIsOnly5MB": MessageLookupByLibrary.simpleMessage(
      "الحد الأقصى لحجم صورة التحميل هو 5 ميجابايت فقط",
    ),
    "maxUploadFilesIsOnly4": MessageLookupByLibrary.simpleMessage(
      "الحد الأقصى لصور التحميل هو 4 فقط",
    ),
    "mealAmount": MessageLookupByLibrary.simpleMessage("كمية الوجبة"),
    "mealType": MessageLookupByLibrary.simpleMessage("نوع الوجبة"),
    "members": MessageLookupByLibrary.simpleMessage("الأعضاء"),
    "message": MessageLookupByLibrary.simpleMessage("الرسالة"),
    "messageSentSuccessfully": MessageLookupByLibrary.simpleMessage(
      "تم إرسال الرسالة بنجاح",
    ),
    "messages": MessageLookupByLibrary.simpleMessage("الرسائل"),
    "mobile": MessageLookupByLibrary.simpleMessage("الهاتف"),
    "monday": MessageLookupByLibrary.simpleMessage("الاثنين"),
    "month": MessageLookupByLibrary.simpleMessage("الشهر"),
    "more": MessageLookupByLibrary.simpleMessage("أكثر"),
    "mother": MessageLookupByLibrary.simpleMessage("الأم"),
    "motherPhoneNumber": MessageLookupByLibrary.simpleMessage("رقم هاتف الأم"),
    "myClass": MessageLookupByLibrary.simpleMessage("فصلي"),
    "myClasses": MessageLookupByLibrary.simpleMessage("فصولي"),
    "name": MessageLookupByLibrary.simpleMessage("الاسم"),
    "next": MessageLookupByLibrary.simpleMessage("التالي"),
    "noActivities": MessageLookupByLibrary.simpleMessage("لا توجد أنشطة"),
    "noActivitiesFound": MessageLookupByLibrary.simpleMessage(
      "لم يتم العثور على أنشطة",
    ),
    "noActivitiesToday": MessageLookupByLibrary.simpleMessage(
      "لا توجد أنشطة اليوم",
    ),
    "noAnnouncements": MessageLookupByLibrary.simpleMessage("لا توجد إعلانات"),
    "noBills": MessageLookupByLibrary.simpleMessage("لا توجد فواتير"),
    "noClasses": MessageLookupByLibrary.simpleMessage("لا توجد فصول"),
    "noData": MessageLookupByLibrary.simpleMessage("لا توجد بيانات"),
    "noEvents": MessageLookupByLibrary.simpleMessage("لا توجد أحداث"),
    "noInvoices": MessageLookupByLibrary.simpleMessage("لا توجد فواتير"),
    "noNotifications": MessageLookupByLibrary.simpleMessage("لا توجد إشعارات"),
    "noPlansForThisMonth": MessageLookupByLibrary.simpleMessage(
      "لا توجد خطط لهذا الشهر",
    ),
    "noQuestions": MessageLookupByLibrary.simpleMessage("لا توجد أسئلة"),
    "noStudents": MessageLookupByLibrary.simpleMessage("لا يوجد طلاب"),
    "noSupplies": MessageLookupByLibrary.simpleMessage("لا توجد مستلزمات"),
    "noTeachers": MessageLookupByLibrary.simpleMessage("لا يوجد مدرسون"),
    "none": MessageLookupByLibrary.simpleMessage("لا شيء"),
    "note": MessageLookupByLibrary.simpleMessage("الملاحظة"),
    "notifications": MessageLookupByLibrary.simpleMessage("الإشعارات"),
    "nurseryActivities": MessageLookupByLibrary.simpleMessage(
      "إضافة أنشطة الحضانة",
    ),
    "nurseryLogo": MessageLookupByLibrary.simpleMessage("شعار الحضانة"),
    "nurseryName": MessageLookupByLibrary.simpleMessage("اسم الحضانة"),
    "parentPhoneNumber": MessageLookupByLibrary.simpleMessage(
      "رقم هاتف الوالدين",
    ),
    "password": MessageLookupByLibrary.simpleMessage("كلمة المرور"),
    "passwordConfirmation": MessageLookupByLibrary.simpleMessage(
      "تأكيد كلمة المرور",
    ),
    "passwordsDoNotMatch": MessageLookupByLibrary.simpleMessage(
      "كلمات المرور غير متطابقة",
    ),
    "passwordsShouldMatch": MessageLookupByLibrary.simpleMessage(
      "يجب أن تتطابق كلمات المرور",
    ),
    "persons": MessageLookupByLibrary.simpleMessage("أشخاص"),
    "phoneNumber": MessageLookupByLibrary.simpleMessage("رقم الهاتف"),
    "pickImage": MessageLookupByLibrary.simpleMessage("اختيار صورة"),
    "pickupPerson": MessageLookupByLibrary.simpleMessage("شخص الاستلام"),
    "pickupPersonAlreadyExists": MessageLookupByLibrary.simpleMessage(
      "شخص الاستلام موجود بالفعل",
    ),
    "pickupPersons": MessageLookupByLibrary.simpleMessage("أشخاص الاستلام"),
    "plans": MessageLookupByLibrary.simpleMessage("الخطط"),
    "pleaseAcceptTerms": MessageLookupByLibrary.simpleMessage(
      "الرجاء قبول الشروط",
    ),
    "pleasePickAnImage": MessageLookupByLibrary.simpleMessage(
      "الرجاء اختيار صورة",
    ),
    "pleaseUseConnectifyAdminTeachersApplication":
        MessageLookupByLibrary.simpleMessage(
          "يرجى استخدام تطبيق Connectify Admin & Teachers",
        ),
    "pleaseVerifyPhone": MessageLookupByLibrary.simpleMessage(
      "الرجاء التحقق من الهاتف",
    ),
    "privacyPolicy": MessageLookupByLibrary.simpleMessage("سياسة الخصوصية"),
    "profile": MessageLookupByLibrary.simpleMessage("الملف الشخصي"),
    "question": MessageLookupByLibrary.simpleMessage("سؤال"),
    "reports": MessageLookupByLibrary.simpleMessage("التقارير"),
    "required": MessageLookupByLibrary.simpleMessage("مطلوب"),
    "requiredSupplies": MessageLookupByLibrary.simpleMessage(
      "المستلزمات المطلوبة",
    ),
    "resendCode": MessageLookupByLibrary.simpleMessage("إعادة إرسال الرمز"),
    "resetPassword": MessageLookupByLibrary.simpleMessage(
      "إعادة تعيين كلمة المرور",
    ),
    "results": MessageLookupByLibrary.simpleMessage("النتائج"),
    "saturday": MessageLookupByLibrary.simpleMessage("السبت"),
    "save": MessageLookupByLibrary.simpleMessage("حفظ"),
    "searchQuestion": MessageLookupByLibrary.simpleMessage("البحث عن سؤال"),
    "sections": MessageLookupByLibrary.simpleMessage("الأقسام"),
    "seeAll": MessageLookupByLibrary.simpleMessage("عرض الكل"),
    "seeAllActivities": MessageLookupByLibrary.simpleMessage(
      "رؤية جميع الأنشطة",
    ),
    "selectPeriod": MessageLookupByLibrary.simpleMessage("اختر الفترة"),
    "send": MessageLookupByLibrary.simpleMessage("إرسال"),
    "sendANewMessage": MessageLookupByLibrary.simpleMessage(
      "إرسال رسالة جديدة",
    ),
    "sendANewMessageTo": m0,
    "sendANewMessageToAdmin": MessageLookupByLibrary.simpleMessage(
      "إرسال رسالة إلى\nالمسؤول",
    ),
    "sendANewMessageToTeacher": MessageLookupByLibrary.simpleMessage(
      "إرسال رسالة إلى\nالمدرس",
    ),
    "sendSupplies": MessageLookupByLibrary.simpleMessage("إرسال المستلزمات"),
    "sendSupplyToStudent": MessageLookupByLibrary.simpleMessage(
      "إرسال المستلزمات للطالب",
    ),
    "sent": MessageLookupByLibrary.simpleMessage("تم الإرسال"),
    "sentVerificationCode": MessageLookupByLibrary.simpleMessage(
      "لقد أرسلنا رمز تحقق إلى",
    ),
    "settings": MessageLookupByLibrary.simpleMessage("الإعدادات"),
    "setupYourClasses": MessageLookupByLibrary.simpleMessage("إعداد فصولك"),
    "signIn": MessageLookupByLibrary.simpleMessage("تسجيل الدخول"),
    "signUp": MessageLookupByLibrary.simpleMessage("اشتراك"),
    "singleActivity": MessageLookupByLibrary.simpleMessage("نشاط واحد"),
    "skip": MessageLookupByLibrary.simpleMessage("تخطي"),
    "skipForNow": MessageLookupByLibrary.simpleMessage("تخطي الآن"),
    "sleep": MessageLookupByLibrary.simpleMessage("النوم"),
    "snack": MessageLookupByLibrary.simpleMessage("الوجبة الخفيفة"),
    "some": MessageLookupByLibrary.simpleMessage("بعض"),
    "speakWithConfidence": MessageLookupByLibrary.simpleMessage("تحدث بثقة"),
    "staff": MessageLookupByLibrary.simpleMessage("الموظفين"),
    "stool": MessageLookupByLibrary.simpleMessage("البراز"),
    "studentAndClass": MessageLookupByLibrary.simpleMessage("الطالب والفصل"),
    "studentIsAbsentToday": m1,
    "studentName": MessageLookupByLibrary.simpleMessage("اسم الطالب"),
    "students": MessageLookupByLibrary.simpleMessage("الطلاب"),
    "submit": MessageLookupByLibrary.simpleMessage("إرسال"),
    "subscriptionExpiredPleaseContactSupport":
        MessageLookupByLibrary.simpleMessage(
          "انتهى الاشتراك، يرجى الاتصال بالدعم !",
        ),
    "supplies": MessageLookupByLibrary.simpleMessage("المستلزمات"),
    "supplyName": MessageLookupByLibrary.simpleMessage("اسم المستلزمات"),
    "tClass": MessageLookupByLibrary.simpleMessage("الفصل"),
    "teacher": MessageLookupByLibrary.simpleMessage("مدرس"),
    "teacherInfo": MessageLookupByLibrary.simpleMessage("معلومات المدرس"),
    "teacherName": MessageLookupByLibrary.simpleMessage("اسم المدرس"),
    "teacherSignUp": MessageLookupByLibrary.simpleMessage("تسجيل المدرس"),
    "teachers": MessageLookupByLibrary.simpleMessage("المدرسون"),
    "team": MessageLookupByLibrary.simpleMessage("الفريق"),
    "thursday": MessageLookupByLibrary.simpleMessage("الخميس"),
    "title": MessageLookupByLibrary.simpleMessage("العنوان"),
    "to": MessageLookupByLibrary.simpleMessage("إلى"),
    "toAdmin": MessageLookupByLibrary.simpleMessage("للمسؤول"),
    "toTeacher": MessageLookupByLibrary.simpleMessage("للمدرس"),
    "today": MessageLookupByLibrary.simpleMessage("اليوم"),
    "todaysActivities": MessageLookupByLibrary.simpleMessage("أنشطة اليوم"),
    "toilet": MessageLookupByLibrary.simpleMessage("الحمام"),
    "toiletType": MessageLookupByLibrary.simpleMessage("نوع الحمام"),
    "tuesday": MessageLookupByLibrary.simpleMessage("الثلاثاء"),
    "unAssign": MessageLookupByLibrary.simpleMessage("UnAssign"),
    "update": MessageLookupByLibrary.simpleMessage("تحديث"),
    "updateRequired": MessageLookupByLibrary.simpleMessage(
      "مطلوب تحديث للاستمرار في استخدام التطبيق. يرجى تحديثه الآن.",
    ),
    "updatedSuccessfully": MessageLookupByLibrary.simpleMessage(
      "تم التحديث بنجاح",
    ),
    "uploadLogo": MessageLookupByLibrary.simpleMessage("تحميل الشعار"),
    "urine": MessageLookupByLibrary.simpleMessage("البول"),
    "userNotFound": MessageLookupByLibrary.simpleMessage(
      "لم يتم العثور على المستخدم",
    ),
    "validateYourPhoneFirstPlease": MessageLookupByLibrary.simpleMessage(
      "الرجاء التحقق من هاتفك أولاً",
    ),
    "verificationCodeIsWrong": MessageLookupByLibrary.simpleMessage(
      "رمز التحقق خاطئ",
    ),
    "verificationSuccessful": MessageLookupByLibrary.simpleMessage(
      "تم التحقق بنجاح",
    ),
    "verify": MessageLookupByLibrary.simpleMessage("التحقق"),
    "warning": MessageLookupByLibrary.simpleMessage("تنبيه"),
    "wednesday": MessageLookupByLibrary.simpleMessage("الأربعاء"),
    "weeklyActivity": MessageLookupByLibrary.simpleMessage("نشاط أسبوعي"),
    "youCannotDeleteThisQuestionBecauseitsHasStudentResults":
        MessageLookupByLibrary.simpleMessage(
          "لا يمكنك حذف هذا السؤال لأنه يحتوي على نتائج للطلاب",
        ),
  };
}
