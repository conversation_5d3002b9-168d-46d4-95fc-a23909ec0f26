{"speakWithConfidence": "Speak with confidence", "getTalkingFrom": "Every day is a journey. \nSign in to join us.", "signUp": "Sign Up", "alreadyHaveAnAccount": "Already have an account?", "dontHaveAnAccount": "You don’t have account?", "signIn": "Sign In", "SignupAsa": "Signup as a", "administrator": "Administrator", "teacher": "Teacher", "phoneNumber": "Phone Number", "password": "Password", "enter": "Enter", "adminSignUp": "Administrator Sign up", "teacherSignUp": "Teacher Sign up", "nurseryLogo": "Nursery Logo", "nurseryName": "Nursery Name", "email": "Email", "iHaveReadThe": "I have read the", "privacyPolicy": "Privacy Policy", "finishLetsStart": "Finish, let’s start", "uploadLogo": "Upload logo", "setupYourClasses": "Setup your classes", "addNewClass": "Add New Class", "editClass": "Edit Class", "SkipForNow": "Skip for now", "goodMorning": "Good Morning!", "goodAfternoon": "Good Afternoon!", "classes": "Classes", "className": "Class Name", "classDescription": "Class Description", "team": "Team", "students": "Students", "activities": "Activities", "staff": "Staff", "addStudents": "Add Students", "addNewStudents": "Add New Students", "skipForNow": "Skip for Now", "addNurseryTeam": "Add Nursery Team", "addNurseryTeamMember": "Add New Team Member", "skip": "<PERSON><PERSON>", "letsStart": "Let’s Start", "nurseryActivities": "Add a Nursery Activities", "addNurseryActivities": "Add New Activity ", "maxUploadFilesIsOnly4": "Max upload images is only 4", "maxUploadFileSizeIsOnly5MB": "Max upload image size is only 5 MB", "congratulations": "Congratulations", "letsDoAGreatJob": "Let’s do a great job", "dashboard": "Dashboard", "messages": "Messages", "settings": "Settings", "home": "Home", "attendeesOfToday": "Attendees of Today", "Of": "Of", "currentActivity": "Current Activity", "attendance": "Attendance", "financial": "Financial", "emergency": "Emergency", "events": "Events", "members": "Members", "eventThisMonth": "Event this month", "createNewClass": "Create new class", "back": "Back", "myClasses": "My Classes", "myClass": "My Class", "teacherInfo": "Teacher info", "dailySchedule": "Daily Schedule", "pickImage": "Pick Image", "save": "Save", "createANewClass": "Create a new class", "createANewSupply": "Add a new Supply", "addANewStaffMember": "Add a new staff member", "teacherName": "Teacher Name", "supplyName": "supply Name", "description": "Description", "pleasePickAnImage": "Please pick an image", "studentName": "Student Name", "matherPhoneNumber": "Mather Phone number", "homeAddress": "Home Address", "addParentsPhoneNumber": "Add Parents Phone number", "birthDate": "Birth Date", "activityName": "Activity Name", "activityDescription": "Activity Description", "next": "Next", "motherPhoneNumber": "Mother Phone number", "parentPhoneNumber": "Parent Phone number", "attendanceChart": "Attendance chart", "studentAndClass": "Student & Class", "selectPeriod": "Select period", "attended": "attended", "absent": "absent", "incomeChart": "Income chart", "activitiesCompleted": "Activities completed", "billsChart": "Bills chart", "invoicesChart": "Invoices chart", "currentMonth": "Current month", "assignToClass": "Assign to class", "assign": "Assign", "assigned": "Assigned", "invoices": "Invoices ", "bills": "Bills", "addNewBill": "Add New Bill", "from": "From", "to": "To", "addNewInvoice": "Add New Invoice", "edit": "Edit", "delete": "Delete", "date": "Date", "billName": "<PERSON>", "billAmount": "<PERSON>", "confirm": "Confirm", "confirmation": "Confirmation", "cancel": "Cancel", "areYouSureToDeleteThisBill": "Are you sure to delete this bill ?", "areYouSureToDeleteThisInvoice": "Are you sure to delete this invoice ?", "areYouSureToDeleteThisClass": "Are you sure to delete this class ?", "areYouSureToDeleteThisSupply": "Are you sure to delete this supply ?", "areYouSureToDeleteThisTeacher": "Are you sure to delete this teacher ?", "areYouSureToDeleteThisStudent": "Are you sure to delete this student ?", "areYouSureToDeleteThisActivity": "Are you sure to delete this activity ?", "invoiceAmount": "Invoice Amount", "invoiceName": "Invoice Name", "deletedSuccessfully": "deleted successfully", "editSuccessfully": "Edit successfully", "editTeacher": "Edit Teacher", "addedSuccessfully": "Added successfully", "didNotGetCode": "Didn’t get the code?", "resendCode": "Resend Code", "completeVerification": "Complete Verification", "errorOccurred": "Error occurred", "verificationCodeIsWrong": "Verification code is wrong", "enterOtp": "Enter OTP", "sentVerificationCode": "We sent a verification code to", "submit": "Submit", "verify": "verify", "enterValidPhoneNumber": "Enter valid phone number", "enterPhoneNumberFirst": "First enter your phone number", "verificationSuccessful": "Verification successful", "pleaseAcceptTerms": "Please accept terms", "pleaseVerifyPhone": "Please verify phone", "noEvents": "No events", "noBills": "No Bills", "noInvoices": "No Invoices", "noClasses": "No Classes", "noNotifications": "No Notifications", "noTeachers": "No Teachers", "noStudents": "No Students", "noActivities": "No Activities", "noSupplies": "No Supplies", "active": "Active", "addNewEvent": "Add New Event", "eventName": "Event Name", "eventType": "Event Type", "mother": "Mother", "father": "Father", "address": "Address", "title": "Title", "message": "Message", "sendANewMessage": "Send a new message", "sendANewMessageTo": "Send a new message to {name}", "areYouSureToDeleteThisEvent": "Are you sure to delete this event ?", "editEvent": "Edit Event", "teachers": "Teachers", "resetPassword": "Reset your password", "forgetPassword": "Forget Password", "enterNewPassword": "Enter new password", "passwordsShouldMatch": "Passwords should match", "confirmNewPassword": "Confirm new password", "userNotFound": "User not Found", "add": "Add", "supplies": "Supplies", "food": "Food", "toilet": "<PERSON><PERSON><PERSON>", "sleep": "Sleep", "breakfast": "Breakfast", "snack": "Snack", "lunch": "Lunch", "all": "All", "more": "More", "some": "Some", "none": "None", "urine": "<PERSON><PERSON>", "day": "Day", "stool": "Stool", "inClothes": "<PERSON><PERSON><PERSON>", "inTheDiaper": "Diaper", "notifications": "Notifications", "announcements": "Announcements", "noAnnouncements": "No Announcements", "inTheToilet": "<PERSON><PERSON><PERSON>", "toiletType": "Toilet Type", "today": "Today", "monday": "Monday", "tuesday": "Tuesday", "wednesday": "Wednesday", "thursday": "Thursday", "friday": "Friday", "saturday": "Saturday", "mealType": "Meal Type", "mealAmount": "meal Amount", "tClass": "Class", "assignSupplyToStudent": "Assign Supply To Student", "sendSupplyToStudent": "Send Supply To Student", "sendSupplies": "Send Supplies", "assignActivityToClass": "Assign Activity To Class", "logout": "Logout", "attendanceTracking": "Attendance Tracking", "reports": "Reports", "update": "Update", "activityChart": "Activity chart", "month": "Month", "todaysActivities": "Today’s activities", "noActivitiesFound": "No activities found", "seeAllActivities": "See all activities", "seeAll": "See all", "requiredSupplies": "Required supplies", "required": "Required", "markAsSent": "<PERSON> as <PERSON><PERSON>", "markAsRead": "<PERSON> <PERSON>", "maxStudentsReachedPleaseContactSupport": "Max students reached, please contact support !", "changeLanguage": "Change Language", "english": "English", "arabic": "Arabic", "emergencyInformation": "Emergency Information", "addTheEmergency": "Add the emergency data that helps the nursery reach you out", "name": "Name", "mobile": "Mobile", "sendANewMessageToAdmin": "Send message to\nAdmin", "sendANewMessageToTeacher": "Send message to\nTeacher", "send": "Send", "admin": "Admin", "sent": "<PERSON><PERSON>", "updatedSuccessfully": "Updated successfully", "toTeacher": "To Teacher", "toAdmin": "To Admin", "validateYourPhoneFirstPlease": "Validate your phone first please", "unAssign": "UnAssign", "messageSentSuccessfully": "Message sent successfully", "profile": "Profile", "noQuestions": "No questions", "searchQuestion": "Search Question", "question": "Question", "exams": "<PERSON><PERSON>", "addNewQuestion": "Add New Question", "areYouSureToDeleteThisQuestion": "Are you sure to delete this question ?", "youCannotDeleteThisQuestionBecauseitsHasStudentResults": "You cannot delete this question because it's has student results", "changePassword": "Change Password", "passwordsDoNotMatch": "Passwords do not match", "passwordConfirmation": "Password Confirmation", "studentIsAbsentToday": "{Student} is absent today", "noActivitiesToday": "No activities today", "deleteAccount": "Delete Account", "areYouSureToDeleteYourAccount": "Are you sure to delete your account?", "note": "Note", "results": "Results", "singleActivity": "Single Activity", "weeklyActivity": "Weekly Activity", "noData": "No data", "updateRequired": "An update is required to continue using the app. Please update it now.", "contactSupport": "Contact Support", "pickupPersons": "Pickup Persons", "warning": "Warning", "pickupPersonAlreadyExists": "Pickup person already exists", "pickupPerson": "Pickup Person", "persons": "Persons", "addPickupPerson": "Add Pickup Person", "enterPickupPerson": "Enter pickup person", "downloadedSuccessfully": "Downloaded successfully", "subscriptionExpiredPleaseContactSupport": "Subscription expired, please contact support !", "pleaseUseConnectifyAdminTeachersApplication": "Please use Connectify Admin & Teachers application"}